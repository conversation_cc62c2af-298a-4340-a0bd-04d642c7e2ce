# مدونة عربية - Arabic Blog

مدونة عربية ديناميكية مبنية باستخدام Django و Tailwind CSS مع دعم كامل للغة العربية واتجاه RTL.

## المميزات

### التقنيات المستخدمة
- **Backend**: Python 3.9+ و Django 4.2+
- **Frontend**: Tailwind CSS 3+ عبر CDN
- **Database**: SQLite (للتطوير) / PostgreSQL (للإنتاج)
- **Font**: IBM Plex Sans Arabic من Google Fonts
- **RTL Support**: دعم كامل لاتجاه RTL

### الميزات الأساسية

#### 🛠 لوحة الإدارة
- لوحة إدارة Django مخصصة بالعربية
- إدارة كاملة للمقالات (CRUD)
- إدارة التصنيفات والوسوم
- نظام نشر متقدم (مسودة، منشور، مجدول)
- عداد المشاهدات

#### 🏠 الواجهة الأمامية
- **الصفحة الرئيسية**: عرض المقالات الحديثة مع ترقيم الصفحات
- **صفحة المقال**: عرض كامل للمقال مع أزرار المشاركة
- **صفحات التصنيفات والوسوم**: فلترة المقالات
- **صفحة البحث**: بحث في العناوين والمحتوى
- **صفحة من نحن**: معلومات عن المدونة
- **صفحة اتصل بنا**: نموذج تواصل مع التحقق

#### 💅 التصميم والتجربة
- خط IBM Plex Sans Arabic
- تصميم متجاوب (Responsive)
- دعم كامل لـ RTL
- ألوان هادئة ومريحة للعين
- انتقالات سلسة
- تحسين للقراءة العربية

#### 🔒 الأمان والتحسين
- حماية CSRF
- التحقق من صحة النماذج
- روابط SEO-friendly
- Meta tags للمشاركة الاجتماعية
- إعدادات الإنتاج

## التثبيت والإعداد

### المتطلبات
- Python 3.9+
- pip
- virtualenv (اختياري لكن مُوصى به)

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd arabic-blog
```

2. **إنشاء البيئة الافتراضية**
```bash
python3 -m venv venv
source venv/bin/activate  # على Linux/Mac
# أو
venv\Scripts\activate  # على Windows
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

4. **إعداد قاعدة البيانات**
```bash
python manage.py migrate
```

5. **إنشاء مستخدم إداري**
```bash
python manage.py createsuperuser
```

6. **إضافة بيانات تجريبية (اختياري)**
```bash
python manage.py populate_blog
```

7. **تشغيل الخادم**
```bash
python manage.py runserver
```

8. **فتح المتصفح**
- الموقع: http://127.0.0.1:8000/
- لوحة الإدارة: http://127.0.0.1:8000/admin/

## الاستخدام

### إضافة محتوى جديد
1. ادخل إلى لوحة الإدارة
2. أضف تصنيفات ووسوم
3. أنشئ مقالات جديدة
4. اختر حالة النشر المناسبة

### تخصيص التصميم
- الألوان الأساسية في `templates/base.html`
- الأنماط المخصصة في `static/src/input.css`
- إعدادات Tailwind في script tag

### إعدادات الإنتاج
1. قم بتحديث `ALLOWED_HOSTS` في settings.py
2. اضبط `DEBUG = False`
3. اضبط قاعدة بيانات PostgreSQL
4. اضبط إعدادات البريد الإلكتروني
5. اجمع الملفات الثابتة: `python manage.py collectstatic`

## هيكل المشروع

```
arabic-blog/
├── arabic_blog/          # إعدادات المشروع الرئيسية
├── blog/                 # تطبيق المدونة
│   ├── models.py        # نماذج البيانات
│   ├── views.py         # العروض
│   ├── admin.py         # إعدادات الإدارة
│   ├── urls.py          # روابط التطبيق
│   └── templates/       # قوالب HTML
├── templates/           # القوالب الأساسية
├── static/             # الملفات الثابتة
├── media/              # ملفات الوسائط المرفوعة
├── requirements.txt    # متطلبات Python
└── manage.py          # أداة إدارة Django
```

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

للدعم والاستفسارات:
- البريد الإلكتروني: <EMAIL>
- تويتر: @ArabicBlog

---

تم تطويره بـ ❤️ للمجتمع العربي التقني
