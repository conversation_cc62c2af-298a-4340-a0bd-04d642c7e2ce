/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './templates/**/*.html',
    './blog/templates/**/*.html',
    './static/js/**/*.js',
  ],
  theme: {
    extend: {
      fontFamily: {
        'arabic': ['"IBM Plex Sans Arabic"', 'sans-serif'],
      },
      colors: {
        'primary': {
          50: '#f0fdfa',
          100: '#ccfbf1',
          200: '#99f6e4',
          300: '#5eead4',
          400: '#2dd4bf',
          500: '#14b8a6',
          600: '#0d9488',
          700: '#0f766e',
          800: '#115e59',
          900: '#134e4a',
        },
        'secondary': {
          50: '#fafafa',
          100: '#f4f4f5',
          200: '#e4e4e7',
          300: '#d4d4d8',
          400: '#a1a1aa',
          500: '#71717a',
          600: '#52525b',
          700: '#3f3f46',
          800: '#27272a',
          900: '#18181b',
        }
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      typography: {
        DEFAULT: {
          css: {
            'direction': 'rtl',
            'text-align': 'right',
            'font-family': '"IBM Plex Sans Arabic", sans-serif',
            'line-height': '1.8',
            'h1, h2, h3, h4, h5, h6': {
              'font-family': '"IBM Plex Sans Arabic", sans-serif',
              'font-weight': '700',
            },
            'p': {
              'margin-bottom': '1.5rem',
            },
            'ul, ol': {
              'padding-right': '1.5rem',
              'padding-left': '0',
            },
            'blockquote': {
              'border-right': '4px solid #14b8a6',
              'border-left': 'none',
              'padding-right': '1rem',
              'padding-left': '0',
              'margin-right': '0',
            },
          },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
  ],
}
