@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    direction: rtl;
    font-family: 'IBM Plex Sans Arabic', sans-serif;
  }
  
  body {
    @apply font-arabic text-gray-900 bg-gray-50;
    line-height: 1.8;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-arabic font-bold;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-secondary-200 hover:bg-secondary-300 text-secondary-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  .form-textarea {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-vertical;
  }
  
  .nav-link {
    @apply text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
  }
  
  .nav-link-active {
    @apply text-primary-600 bg-primary-50 px-3 py-2 rounded-md text-sm font-medium;
  }
  
  .post-card {
    @apply card hover:shadow-lg transition-shadow duration-300;
  }
  
  .post-meta {
    @apply text-sm text-gray-500 flex items-center gap-2;
  }
  
  .tag {
    @apply inline-block bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full;
  }
  
  .breadcrumb {
    @apply flex items-center space-x-2 text-sm text-gray-500;
  }
  
  .breadcrumb-item {
    @apply hover:text-primary-600 transition-colors duration-200;
  }
  
  .pagination {
    @apply flex items-center justify-center space-x-1;
  }
  
  .pagination-link {
    @apply px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200;
  }
  
  .pagination-link-active {
    @apply px-3 py-2 text-sm text-white bg-primary-600 border border-primary-600;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .rtl-flip {
    transform: scaleX(-1);
  }
  
  .writing-mode-vertical {
    writing-mode: vertical-rl;
  }
}
