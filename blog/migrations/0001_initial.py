# Generated by Django 4.2.23 on 2025-07-14 08:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم التصنيف')),
                ('slug', models.SlugField(max_length=100, unique=True, verbose_name='الرابط')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'تصنيف',
                'verbose_name_plural': 'التصنيفات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='اسم الوسم')),
                ('slug', models.SlugField(unique=True, verbose_name='الرابط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'وسم',
                'verbose_name_plural': 'الوسوم',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Post',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('slug', models.SlugField(max_length=200, unique=True, verbose_name='الرابط')),
                ('content', models.TextField(verbose_name='المحتوى')),
                ('excerpt', models.TextField(blank=True, max_length=300, verbose_name='المقتطف')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('published', 'منشور'), ('scheduled', 'مجدول')], default='draft', max_length=10, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('published_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ النشر')),
                ('views_count', models.PositiveIntegerField(default=0, verbose_name='عدد المشاهدات')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='الكاتب')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='blog.category', verbose_name='التصنيف')),
                ('tags', models.ManyToManyField(blank=True, to='blog.tag', verbose_name='الوسوم')),
            ],
            options={
                'verbose_name': 'مقال',
                'verbose_name_plural': 'المقالات',
                'ordering': ['-created_at'],
            },
        ),
    ]
