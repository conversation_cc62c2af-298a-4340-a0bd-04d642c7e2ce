from django.shortcuts import render, get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from django.core.mail import send_mail
from django.conf import settings
from django.contrib import messages
from .models import Post, Category, Tag


def home(request):
    """Homepage view with recent posts"""
    posts = Post.objects.filter(status='published').select_related('category', 'author').prefetch_related('tags')

    # Pagination
    paginator = Paginator(posts, 6)  # Show 6 posts per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get categories for sidebar
    categories = Category.objects.all()

    # Get popular tags
    popular_tags = Tag.objects.all()[:10]

    context = {
        'page_obj': page_obj,
        'categories': categories,
        'popular_tags': popular_tags,
    }
    return render(request, 'blog/home.html', context)


def post_detail(request, slug):
    """Individual post detail view"""
    post = get_object_or_404(Post, slug=slug, status='published')

    # Increment view count
    post.increment_views()

    # Get related posts from same category
    related_posts = Post.objects.filter(
        category=post.category,
        status='published'
    ).exclude(id=post.id)[:3]

    context = {
        'post': post,
        'related_posts': related_posts,
    }
    return render(request, 'blog/post_detail.html', context)


def category_posts(request, slug):
    """Posts filtered by category"""
    category = get_object_or_404(Category, slug=slug)
    posts = Post.objects.filter(category=category, status='published').select_related('author').prefetch_related('tags')

    # Pagination
    paginator = Paginator(posts, 6)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'category': category,
        'page_obj': page_obj,
    }
    return render(request, 'blog/category_posts.html', context)


def tag_posts(request, slug):
    """Posts filtered by tag"""
    tag = get_object_or_404(Tag, slug=slug)
    posts = Post.objects.filter(tags=tag, status='published').select_related('category', 'author').prefetch_related('tags')

    # Pagination
    paginator = Paginator(posts, 6)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'tag': tag,
        'page_obj': page_obj,
    }
    return render(request, 'blog/tag_posts.html', context)


def search(request):
    """Search posts"""
    query = request.GET.get('q', '')
    posts = []

    if query:
        posts = Post.objects.filter(
            Q(title__icontains=query) |
            Q(content__icontains=query) |
            Q(excerpt__icontains=query),
            status='published'
        ).select_related('category', 'author').prefetch_related('tags')

    # Pagination
    paginator = Paginator(posts, 6)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'query': query,
        'page_obj': page_obj,
    }
    return render(request, 'blog/search.html', context)


def about(request):
    """About page"""
    return render(request, 'blog/about.html')


def contact(request):
    """Contact page with form handling"""
    if request.method == 'POST':
        name = request.POST.get('name')
        email = request.POST.get('email')
        subject = request.POST.get('subject')
        message = request.POST.get('message')

        if name and email and subject and message:
            # Send email (configure email settings in production)
            try:
                send_mail(
                    f'رسالة من {name}: {subject}',
                    f'من: {name} ({email})\n\n{message}',
                    email,
                    ['<EMAIL>'],  # Replace with your email
                    fail_silently=False,
                )
                messages.success(request, 'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.')
            except Exception as e:
                messages.error(request, 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.')
        else:
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')

    return render(request, 'blog/contact.html')
