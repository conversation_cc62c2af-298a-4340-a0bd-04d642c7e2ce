from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from blog.models import Category, Tag, Post
from django.utils import timezone
import random


class Command(BaseCommand):
    help = 'Populate the blog with sample data'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample blog data...')

        # Create categories
        categories_data = [
            {'name': 'تطوير الويب', 'description': 'مقالات حول تطوير المواقع والتطبيقات'},
            {'name': 'البرمجة', 'description': 'دروس ونصائح في البرمجة'},
            {'name': 'الذكاء الاصطناعي', 'description': 'آخر التطورات في مجال الذكاء الاصطناعي'},
            {'name': 'أمن المعلومات', 'description': 'مواضيع حول أمن المعلومات والحماية'},
            {'name': 'تقنيات حديثة', 'description': 'أحدث التقنيات والابتكارات'},
        ]

        categories = []
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            categories.append(category)
            if created:
                self.stdout.write(f'Created category: {category.name}')

        # Create tags
        tags_data = [
            'Python', 'Django', 'JavaScript', 'React', 'Vue.js', 'Node.js',
            'HTML', 'CSS', 'Bootstrap', 'Tailwind', 'MySQL', 'PostgreSQL',
            'MongoDB', 'Redis', 'Docker', 'Kubernetes', 'AWS', 'Azure',
            'Git', 'GitHub', 'API', 'REST', 'GraphQL', 'Machine Learning',
            'Deep Learning', 'TensorFlow', 'PyTorch', 'Data Science',
            'Cybersecurity', 'Blockchain', 'IoT', 'Mobile Development'
        ]

        tags = []
        for tag_name in tags_data:
            tag, created = Tag.objects.get_or_create(name=tag_name)
            tags.append(tag)
            if created:
                self.stdout.write(f'Created tag: {tag.name}')

        # Get or create admin user
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'مدير',
                'last_name': 'المدونة',
                'is_staff': True,
                'is_superuser': True
            }
        )

        # Sample posts data
        posts_data = [
            {
                'title': 'مقدمة في تطوير الويب باستخدام Django',
                'content': '''
Django هو إطار عمل قوي لتطوير تطبيقات الويب باستخدام لغة Python. يتميز Django بسهولة الاستخدام والأمان العالي.

## ما هو Django؟

Django هو إطار عمل مفتوح المصدر يتبع نمط Model-View-Template (MVT). تم تطويره لمساعدة المطورين على بناء تطبيقات ويب معقدة بسرعة وكفاءة.

## مميزات Django

- **الأمان**: يوفر Django حماية ضد العديد من الثغرات الأمنية الشائعة
- **قابلية التوسع**: يمكن استخدامه لبناء تطبيقات صغيرة ومشاريع ضخمة
- **المرونة**: يدعم قواعد بيانات متعددة ويمكن تخصيصه بسهولة
- **المجتمع**: يحتوي على مجتمع كبير ونشط من المطورين

## البدء مع Django

لبدء مشروع Django جديد، تحتاج إلى تثبيت Django أولاً:

```bash
pip install django
django-admin startproject myproject
cd myproject
python manage.py runserver
```

هذا سيقوم بإنشاء مشروع Django جديد وتشغيل الخادم المحلي.
                ''',
                'category': 'تطوير الويب',
                'tags': ['Python', 'Django']
            },
            {
                'title': 'أساسيات JavaScript للمبتدئين',
                'content': '''
JavaScript هي لغة البرمجة الأساسية للويب. تستخدم لإضافة التفاعل والديناميكية لصفحات الويب.

## ما هي JavaScript؟

JavaScript هي لغة برمجة عالية المستوى ومفسرة. تعمل في المتصفحات وعلى الخوادم باستخدام Node.js.

## المتغيرات في JavaScript

يمكن إعلان المتغيرات باستخدام `var`، `let`، أو `const`:

```javascript
let name = "أحمد";
const age = 25;
var city = "الرياض";
```

## الدوال

الدوال هي كتل من الكود يمكن إعادة استخدامها:

```javascript
function greet(name) {
    return "مرحباً " + name;
}

console.log(greet("سارة"));
```

## الأحداث

يمكن التعامل مع أحداث المستخدم مثل النقر:

```javascript
document.getElementById("myButton").addEventListener("click", function() {
    alert("تم النقر على الزر!");
});
```

JavaScript لغة قوية ومرنة تستحق التعلم لكل مطور ويب.
                ''',
                'category': 'البرمجة',
                'tags': ['JavaScript']
            },
            {
                'title': 'مقدمة في الذكاء الاصطناعي وتعلم الآلة',
                'content': '''
الذكاء الاصطناعي هو أحد أهم المجالات التقنية في العصر الحديث. يهدف إلى إنشاء أنظمة قادرة على محاكاة الذكاء البشري.

## ما هو الذكاء الاصطناعي؟

الذكاء الاصطناعي (AI) هو فرع من علوم الحاسوب يهدف إلى إنشاء آلات ذكية قادرة على أداء مهام تتطلب ذكاءً بشرياً.

## أنواع الذكاء الاصطناعي

### 1. الذكاء الاصطناعي الضيق (Narrow AI)
- متخصص في مهمة واحدة
- مثل: محركات البحث، أنظمة التوصية

### 2. الذكاء الاصطناعي العام (General AI)
- قادر على أداء أي مهمة ذهنية يمكن للإنسان القيام بها
- لا يزال قيد التطوير

## تعلم الآلة

تعلم الآلة هو فرع من الذكاء الاصطناعي يركز على بناء أنظمة تتعلم من البيانات:

- **التعلم المراقب**: التعلم من بيانات مُصنفة
- **التعلم غير المراقب**: اكتشاف الأنماط في البيانات
- **التعلم المعزز**: التعلم من خلال التجربة والخطأ

## التطبيقات العملية

- التعرف على الصور والكلام
- المساعدات الصوتية
- السيارات ذاتية القيادة
- الترجمة الآلية
- التشخيص الطبي

المستقبل مليء بالإمكانيات اللامحدودة للذكاء الاصطناعي.
                ''',
                'category': 'الذكاء الاصطناعي',
                'tags': ['Python', 'TensorFlow', 'Machine Learning']
            },
            {
                'title': 'أمن المعلومات: أفضل الممارسات للمطورين',
                'content': '''
أمن المعلومات أمر بالغ الأهمية في تطوير التطبيقات. يجب على كل مطور فهم أساسيات الأمان.

## أهمية أمن المعلومات

في عالم متصل رقمياً، حماية البيانات والأنظمة أمر حيوي لنجاح أي مشروع تقني.

## التهديدات الشائعة

### 1. حقن SQL (SQL Injection)
- استخدام استعلامات محضرة (Prepared Statements)
- التحقق من صحة المدخلات

### 2. البرمجة النصية عبر المواقع (XSS)
- تنظيف المدخلات
- استخدام Content Security Policy

### 3. تزوير الطلبات عبر المواقع (CSRF)
- استخدام رموز CSRF
- التحقق من المرجع

## أفضل الممارسات

### التشفير
```python
import hashlib
import bcrypt

# تشفير كلمات المرور
password = "my_password"
hashed = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
```

### التحقق من الهوية
- استخدام JWT للمصادقة
- تطبيق المصادقة متعددة العوامل
- إدارة الجلسات بأمان

### حماية البيانات
- تشفير البيانات الحساسة
- استخدام HTTPS
- النسخ الاحتياطي المنتظم

### مراقبة الأنظمة
- تسجيل الأحداث الأمنية
- مراقبة الأنشطة المشبوهة
- التحديث المستمر للأنظمة

الأمان ليس خياراً، بل ضرورة في كل مشروع تقني.
                ''',
                'category': 'أمن المعلومات',
                'tags': ['Cybersecurity', 'Python']
            },
            {
                'title': 'مقدمة في React: بناء واجهات المستخدم التفاعلية',
                'content': '''
React هي مكتبة JavaScript قوية لبناء واجهات المستخدم. طورتها Facebook وأصبحت من أشهر أدوات تطوير الويب.

## ما هي React؟

React هي مكتبة JavaScript مفتوحة المصدر لبناء واجهات المستخدم، خاصة للتطبيقات أحادية الصفحة.

## المفاهيم الأساسية

### المكونات (Components)
```jsx
function Welcome(props) {
    return <h1>مرحباً، {props.name}</h1>;
}

function App() {
    return (
        <div>
            <Welcome name="أحمد" />
            <Welcome name="فاطمة" />
        </div>
    );
}
```

### الحالة (State)
```jsx
import { useState } from 'react';

function Counter() {
    const [count, setCount] = useState(0);

    return (
        <div>
            <p>العدد: {count}</p>
            <button onClick={() => setCount(count + 1)}>
                زيادة
            </button>
        </div>
    );
}
```

### الخصائص (Props)
الخصائص هي طريقة لتمرير البيانات من مكون أب إلى مكون فرعي.

## مميزات React

- **الأداء العالي**: Virtual DOM يحسن الأداء
- **قابلية إعادة الاستخدام**: المكونات قابلة للاستخدام مرة أخرى
- **النظام البيئي**: مكتبات وأدوات كثيرة
- **المجتمع**: دعم قوي من المطورين

## البدء مع React

```bash
npx create-react-app my-app
cd my-app
npm start
```

React تجعل بناء واجهات المستخدم أسهل وأكثر متعة!
                ''',
                'category': 'تطوير الويب',
                'tags': ['React', 'JavaScript']
            },
            {
                'title': 'تطوير تطبيقات الهاتف المحمول باستخدام Flutter',
                'content': '''
Flutter هو إطار عمل مفتوح المصدر من Google لتطوير تطبيقات الهاتف المحمول متعددة المنصات.

## ما هو Flutter؟

Flutter يسمح للمطورين ببناء تطبيقات أصلية للـ iOS و Android باستخدام قاعدة كود واحدة.

## مميزات Flutter

### الأداء العالي
- تطبيقات مترجمة إلى كود أصلي
- 60 إطار في الثانية
- لا توجد جسور بطيئة

### تطوير سريع
- Hot Reload للتطوير السريع
- مكتبة واسعة من الـ Widgets
- أدوات تطوير متقدمة

## البدء مع Flutter

```dart
import 'package:flutter/material.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'تطبيق Flutter',
      home: Scaffold(
        appBar: AppBar(
          title: Text('مرحباً بك'),
        ),
        body: Center(
          child: Text('أهلاً وسهلاً!'),
        ),
      ),
    );
  }
}
```

Flutter يجعل تطوير التطبيقات أسهل وأسرع من أي وقت مضى.
                ''',
                'category': 'تطوير الويب',
                'tags': ['Flutter', 'Mobile Development', 'Dart']
            },
            {
                'title': 'مقدمة في علم البيانات والتحليل الإحصائي',
                'content': '''
علم البيانات هو مجال متعدد التخصصات يستخدم الطرق العلمية والخوارزميات لاستخراج المعرفة من البيانات.

## ما هو علم البيانات؟

علم البيانات يجمع بين الإحصاء وعلوم الحاسوب والمعرفة المتخصصة لتحليل البيانات.

## مراحل علم البيانات

### 1. جمع البيانات
- مصادر البيانات المختلفة
- تقنيات الاستخراج
- ضمان جودة البيانات

### 2. تنظيف البيانات
```python
import pandas as pd
import numpy as np

# قراءة البيانات
df = pd.read_csv('data.csv')

# إزالة القيم المفقودة
df = df.dropna()

# تحويل أنواع البيانات
df['date'] = pd.to_datetime(df['date'])
```

### 3. التحليل الاستكشافي
- الإحصاءات الوصفية
- التصور البياني
- اكتشاف الأنماط

### 4. النمذجة
- خوارزميات التعلم الآلي
- التحقق من صحة النماذج
- تحسين الأداء

## أدوات علم البيانات

- **Python**: Pandas, NumPy, Scikit-learn
- **R**: للتحليل الإحصائي
- **SQL**: لإدارة قواعد البيانات
- **Tableau**: للتصور البياني

علم البيانات يفتح آفاقاً جديدة لفهم العالم من حولنا.
                ''',
                'category': 'الذكاء الاصطناعي',
                'tags': ['Data Science', 'Python', 'Machine Learning']
            },
            {
                'title': 'أساسيات الحوسبة السحابية وخدمات AWS',
                'content': '''
الحوسبة السحابية ثورة في عالم التكنولوجيا، وAWS هي الرائدة في هذا المجال.

## ما هي الحوسبة السحابية؟

الحوسبة السحابية هي توفير خدمات الحاسوب عبر الإنترنت بدلاً من الخوادم المحلية.

## مميزات الحوسبة السحابية

### المرونة والتوسع
- توسيع الموارد حسب الحاجة
- دفع مقابل الاستخدام فقط
- إمكانية الوصول من أي مكان

### الأمان
- حماية متقدمة للبيانات
- نسخ احتياطية تلقائية
- مراكز بيانات آمنة

## خدمات AWS الأساسية

### EC2 (Elastic Compute Cloud)
```bash
# إنشاء خادم جديد
aws ec2 run-instances --image-id ami-12345678 --count 1 --instance-type t2.micro
```

### S3 (Simple Storage Service)
- تخزين الملفات والبيانات
- إمكانية الوصول العالمية
- تكلفة منخفضة

### RDS (Relational Database Service)
- قواعد بيانات مُدارة
- نسخ احتياطية تلقائية
- أداء عالي

## أفضل الممارسات

1. **الأمان أولاً**: استخدم IAM لإدارة الصلاحيات
2. **مراقبة التكاليف**: استخدم CloudWatch
3. **النسخ الاحتياطية**: جدولة منتظمة
4. **التحسين**: مراجعة الموارد دورياً

AWS تمكنك من بناء تطبيقات قابلة للتوسع بسهولة.
                ''',
                'category': 'تقنيات حديثة',
                'tags': ['AWS', 'Cloud Computing', 'DevOps']
            },
            {
                'title': 'تطوير واجهات المستخدم الحديثة مع Vue.js',
                'content': '''
Vue.js هو إطار عمل JavaScript تقدمي لبناء واجهات المستخدم التفاعلية.

## لماذا Vue.js؟

Vue.js يجمع بين سهولة التعلم وقوة الأداء، مما يجعله خياراً مثالياً للمطورين.

## المفاهيم الأساسية

### المكونات (Components)
```vue
<template>
  <div class="greeting">
    <h1>{{ message }}</h1>
    <button @click="changeMessage">غير الرسالة</button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      message: 'مرحباً بك في Vue.js!'
    }
  },
  methods: {
    changeMessage() {
      this.message = 'تم تغيير الرسالة!';
    }
  }
}
</script>

<style scoped>
.greeting {
  text-align: center;
  padding: 20px;
}
</style>
```

### التوجيهات (Directives)
- `v-if`: العرض الشرطي
- `v-for`: التكرار
- `v-model`: ربط البيانات ثنائي الاتجاه

### إدارة الحالة مع Vuex
```javascript
const store = new Vuex.Store({
  state: {
    count: 0
  },
  mutations: {
    increment(state) {
      state.count++
    }
  }
})
```

## النظام البيئي

- **Vue Router**: للتنقل
- **Vuex**: لإدارة الحالة
- **Vue CLI**: لإنشاء المشاريع
- **Nuxt.js**: للتطبيقات الشاملة

Vue.js يوفر تجربة تطوير ممتعة ومنتجة.
                ''',
                'category': 'تطوير الويب',
                'tags': ['Vue.js', 'JavaScript', 'Frontend']
            },
            {
                'title': 'الأمان السيبراني: حماية التطبيقات من الهجمات',
                'content': '''
الأمان السيبراني أصبح أولوية قصوى في عصر التكنولوجيا الرقمية.

## أنواع التهديدات السيبرانية

### هجمات حقن SQL
```sql
-- مثال على هجوم SQL Injection
SELECT * FROM users WHERE username = 'admin' OR '1'='1' --
```

**الحماية:**
```python
# استخدام Prepared Statements
cursor.execute("SELECT * FROM users WHERE username = %s", (username,))
```

### هجمات XSS (Cross-Site Scripting)
```html
<!-- مثال على هجوم XSS -->
<script>alert('تم اختراق الموقع!');</script>
```

**الحماية:**
- تنظيف المدخلات
- استخدام Content Security Policy
- تشفير البيانات الحساسة

## أفضل ممارسات الأمان

### 1. المصادقة القوية
```python
import bcrypt

# تشفير كلمة المرور
password = "my_secure_password"
hashed = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

# التحقق من كلمة المرور
if bcrypt.checkpw(password.encode('utf-8'), hashed):
    print("كلمة المرور صحيحة")
```

### 2. استخدام HTTPS
- تشفير البيانات أثناء النقل
- حماية من هجمات Man-in-the-Middle
- ثقة المستخدمين

### 3. تحديث النظم
- تطبيق التحديثات الأمنية
- مراقبة الثغرات الجديدة
- اختبار الأمان دورياً

## أدوات الأمان

- **OWASP ZAP**: لاختبار الأمان
- **Nmap**: لفحص الشبكات
- **Wireshark**: لتحليل حركة البيانات

الأمان مسؤولية الجميع، وليس فقط فريق الأمان.
                ''',
                'category': 'أمن المعلومات',
                'tags': ['Cybersecurity', 'Security', 'OWASP']
            },
            {
                'title': 'تطوير APIs باستخدام Node.js و Express',
                'content': '''
Node.js و Express يوفران منصة قوية لبناء APIs سريعة وقابلة للتوسع.

## إعداد مشروع Express

```javascript
const express = require('express');
const app = express();
const port = 3000;

// Middleware
app.use(express.json());

// Routes
app.get('/api/users', (req, res) => {
  res.json({ message: 'قائمة المستخدمين' });
});

app.post('/api/users', (req, res) => {
  const { name, email } = req.body;
  res.json({
    message: 'تم إنشاء المستخدم بنجاح',
    user: { name, email }
  });
});

app.listen(port, () => {
  console.log(`الخادم يعمل على المنفذ ${port}`);
});
```

## أفضل ممارسات تطوير APIs

### 1. استخدام HTTP Status Codes
```javascript
app.get('/api/users/:id', async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({ error: 'المستخدم غير موجود' });
    }
    res.json(user);
  } catch (error) {
    res.status(500).json({ error: 'خطأ في الخادم' });
  }
});
```

### 2. التحقق من صحة البيانات
```javascript
const { body, validationResult } = require('express-validator');

app.post('/api/users',
  body('email').isEmail().withMessage('البريد الإلكتروني غير صحيح'),
  body('name').isLength({ min: 2 }).withMessage('الاسم قصير جداً'),
  (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    // معالجة البيانات
  }
);
```

### 3. الأمان والمصادقة
```javascript
const jwt = require('jsonwebtoken');

// Middleware للمصادقة
const authenticateToken = (req, res, next) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');

  if (!token) {
    return res.status(401).json({ error: 'رمز الوصول مطلوب' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(403).json({ error: 'رمز الوصول غير صحيح' });
  }
};
```

## التوثيق والاختبار

- استخدم Swagger لتوثيق API
- اكتب اختبارات شاملة مع Jest
- راقب الأداء مع New Relic

Node.js يمكنك من بناء APIs قوية وسريعة.
                ''',
                'category': 'البرمجة',
                'tags': ['Node.js', 'Express', 'API', 'JavaScript']
            },
            {
                'title': 'مقدمة في Docker وتقنيات الحاويات',
                'content': '''
Docker ثورة في عالم تطوير ونشر التطبيقات، حيث يوفر بيئة معزولة ومحمولة.

## ما هو Docker؟

Docker هو منصة لتطوير ونشر وتشغيل التطبيقات باستخدام تقنية الحاويات.

## مفاهيم أساسية

### الصور (Images)
```dockerfile
# Dockerfile
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .

EXPOSE 3000

CMD ["npm", "start"]
```

### الحاويات (Containers)
```bash
# بناء الصورة
docker build -t my-app .

# تشغيل الحاوية
docker run -p 3000:3000 my-app

# عرض الحاويات النشطة
docker ps
```

## Docker Compose

```yaml
version: '3.8'
services:
  web:
    build: .
    ports:
      - "3000:3000"
    depends_on:
      - db
    environment:
      - DATABASE_URL=**********************************/myapp

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## مميزات Docker

### الحمولية
- يعمل على أي نظام تشغيل
- نفس البيئة في التطوير والإنتاج
- سهولة النقل بين الخوادم

### الكفاءة
- استخدام أقل للموارد من الآلات الافتراضية
- بدء سريع للحاويات
- مشاركة الطبقات بين الصور

## أفضل الممارسات

1. **استخدم صور أساسية صغيرة**: Alpine Linux
2. **طبقات قليلة**: دمج الأوامر في RUN
3. **ملف .dockerignore**: تجنب نسخ ملفات غير ضرورية
4. **المستخدم غير الجذر**: لأمان أفضل

Docker يبسط عملية النشر ويضمن الاتساق.
                ''',
                'category': 'تقنيات حديثة',
                'tags': ['Docker', 'DevOps', 'Containers']
            },
            {
                'title': 'تطوير تطبيقات الويب التقدمية (PWA)',
                'content': '''
تطبيقات الويب التقدمية تجمع بين أفضل ما في تطبيقات الويب والتطبيقات الأصلية.

## ما هي PWA؟

PWA هي تطبيقات ويب تستخدم تقنيات حديثة لتوفير تجربة مشابهة للتطبيقات الأصلية.

## المكونات الأساسية

### Service Worker
```javascript
// sw.js
const CACHE_NAME = 'my-app-v1';
const urlsToCache = [
  '/',
  '/static/css/main.css',
  '/static/js/main.js'
];

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        return response || fetch(event.request);
      })
  );
});
```

### Web App Manifest
```json
{
  "name": "تطبيقي التقدمي",
  "short_name": "تطبيقي",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#000000",
  "icons": [
    {
      "src": "/icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icon-512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

## مميزات PWA

### العمل دون اتصال
```javascript
// التحقق من حالة الاتصال
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.register('/sw.js')
    .then(registration => {
      console.log('Service Worker مسجل بنجاح');
    });
}

// إشعارات الدفع
if ('Notification' in window) {
  Notification.requestPermission().then(permission => {
    if (permission === 'granted') {
      new Notification('مرحباً من PWA!');
    }
  });
}
```

### التثبيت على الجهاز
```javascript
let deferredPrompt;

window.addEventListener('beforeinstallprompt', (e) => {
  e.preventDefault();
  deferredPrompt = e;

  // إظهار زر التثبيت
  const installButton = document.getElementById('install-button');
  installButton.style.display = 'block';

  installButton.addEventListener('click', () => {
    deferredPrompt.prompt();
    deferredPrompt.userChoice.then((choiceResult) => {
      if (choiceResult.outcome === 'accepted') {
        console.log('المستخدم قبل التثبيت');
      }
      deferredPrompt = null;
    });
  });
});
```

## أدوات التطوير

- **Workbox**: لإدارة Service Workers
- **PWA Builder**: من Microsoft
- **Lighthouse**: لاختبار PWA

PWA تمنحك قوة التطبيقات الأصلية مع مرونة الويب.
                ''',
                'category': 'تطوير الويب',
                'tags': ['PWA', 'Service Worker', 'Web Development']
            },
            {
                'title': 'مقدمة في Blockchain والعملات المشفرة',
                'content': '''
تقنية Blockchain تعيد تشكيل مفهوم الثقة والمعاملات الرقمية.

## ما هو Blockchain؟

Blockchain هو دفتر حسابات رقمي موزع يسجل المعاملات عبر شبكة من الحاسوبات.

## كيف يعمل Blockchain؟

### البلوك (Block)
```javascript
class Block {
  constructor(timestamp, data, previousHash = '') {
    this.timestamp = timestamp;
    this.data = data;
    this.previousHash = previousHash;
    this.hash = this.calculateHash();
    this.nonce = 0;
  }

  calculateHash() {
    return SHA256(this.previousHash + this.timestamp +
                  JSON.stringify(this.data) + this.nonce).toString();
  }

  mineBlock(difficulty) {
    const target = Array(difficulty + 1).join("0");

    while (this.hash.substring(0, difficulty) !== target) {
      this.nonce++;
      this.hash = this.calculateHash();
    }

    console.log(`تم تعدين البلوك: ${this.hash}`);
  }
}
```

### السلسلة (Chain)
```javascript
class Blockchain {
  constructor() {
    this.chain = [this.createGenesisBlock()];
    this.difficulty = 2;
  }

  createGenesisBlock() {
    return new Block(Date.parse("2024-01-01"), "Genesis Block", "0");
  }

  getLatestBlock() {
    return this.chain[this.chain.length - 1];
  }

  addBlock(newBlock) {
    newBlock.previousHash = this.getLatestBlock().hash;
    newBlock.mineBlock(this.difficulty);
    this.chain.push(newBlock);
  }

  isChainValid() {
    for (let i = 1; i < this.chain.length; i++) {
      const currentBlock = this.chain[i];
      const previousBlock = this.chain[i - 1];

      if (currentBlock.hash !== currentBlock.calculateHash()) {
        return false;
      }

      if (currentBlock.previousHash !== previousBlock.hash) {
        return false;
      }
    }

    return true;
  }
}
```

## تطبيقات Blockchain

### العملات المشفرة
- Bitcoin: أول عملة مشفرة
- Ethereum: منصة العقود الذكية
- DeFi: التمويل اللامركزي

### العقود الذكية
```solidity
pragma solidity ^0.8.0;

contract SimpleStorage {
    uint256 private storedData;

    function set(uint256 x) public {
        storedData = x;
    }

    function get() public view returns (uint256) {
        return storedData;
    }
}
```

## مميزات Blockchain

1. **اللامركزية**: لا توجد سلطة مركزية
2. **الشفافية**: جميع المعاملات مرئية
3. **الأمان**: تشفير قوي ومقاومة للتلاعب
4. **عدم القابلية للتغيير**: البيانات لا يمكن تعديلها

Blockchain يفتح آفاقاً جديدة للثقة الرقمية.
                ''',
                'category': 'تقنيات حديثة',
                'tags': ['Blockchain', 'Cryptocurrency', 'Smart Contracts']
            },
            {
                'title': 'تطوير الألعاب باستخدام Unity',
                'content': '''
Unity هو محرك ألعاب قوي يمكن المطورين من إنشاء ألعاب مذهلة لجميع المنصات.

## لماذا Unity؟

Unity يوفر أدوات شاملة لتطوير الألعاب مع دعم لجميع المنصات الرئيسية.

## البدء مع Unity

### إعداد المشهد الأول
```csharp
using UnityEngine;

public class PlayerController : MonoBehaviour
{
    public float speed = 5.0f;
    public float jumpForce = 10.0f;

    private Rigidbody rb;
    private bool isGrounded;

    void Start()
    {
        rb = GetComponent<Rigidbody>();
    }

    void Update()
    {
        // الحركة الأفقية
        float horizontal = Input.GetAxis("Horizontal");
        Vector3 movement = new Vector3(horizontal, 0, 0);
        transform.Translate(movement * speed * Time.deltaTime);

        // القفز
        if (Input.GetKeyDown(KeyCode.Space) && isGrounded)
        {
            rb.AddForce(Vector3.up * jumpForce, ForceMode.Impulse);
            isGrounded = false;
        }
    }

    void OnCollisionEnter(Collision collision)
    {
        if (collision.gameObject.CompareTag("Ground"))
        {
            isGrounded = true;
        }
    }
}
```

## مفاهيم أساسية

### GameObjects و Components
- GameObject: الكائن الأساسي في Unity
- Component: الوظائف المرفقة بالكائن
- Transform: الموقع والدوران والحجم

### الفيزياء في Unity
```csharp
public class BallController : MonoBehaviour
{
    public float bounceForce = 500f;

    void OnCollisionEnter(Collision collision)
    {
        Rigidbody rb = GetComponent<Rigidbody>();
        Vector3 bounceDirection = collision.contacts[0].normal;
        rb.AddForce(-bounceDirection * bounceForce);
    }
}
```

### إدارة الصوت
```csharp
public class AudioManager : MonoBehaviour
{
    public AudioClip jumpSound;
    public AudioClip backgroundMusic;

    private AudioSource audioSource;

    void Start()
    {
        audioSource = GetComponent<AudioSource>();
        audioSource.clip = backgroundMusic;
        audioSource.loop = true;
        audioSource.Play();
    }

    public void PlayJumpSound()
    {
        audioSource.PlayOneShot(jumpSound);
    }
}
```

## تطوير واجهة المستخدم

```csharp
using UnityEngine;
using UnityEngine.UI;

public class UIManager : MonoBehaviour
{
    public Text scoreText;
    public Button pauseButton;

    private int score = 0;

    void Start()
    {
        pauseButton.onClick.AddListener(PauseGame);
        UpdateScoreText();
    }

    public void AddScore(int points)
    {
        score += points;
        UpdateScoreText();
    }

    void UpdateScoreText()
    {
        scoreText.text = "النقاط: " + score;
    }

    void PauseGame()
    {
        Time.timeScale = Time.timeScale == 0 ? 1 : 0;
    }
}
```

## النشر على المنصات المختلفة

- **PC/Mac**: بناء مباشر
- **Mobile**: Android و iOS
- **Console**: PlayStation, Xbox, Nintendo Switch
- **Web**: WebGL

Unity يمكنك من تحويل أفكارك إلى ألعاب حقيقية.
                ''',
                'category': 'البرمجة',
                'tags': ['Unity', 'Game Development', 'C#']
            }
        ]

        # Create posts
        for post_data in posts_data:
            category = Category.objects.get(name=post_data['category'])
            
            post, created = Post.objects.get_or_create(
                title=post_data['title'],
                defaults={
                    'content': post_data['content'],
                    'author': admin_user,
                    'category': category,
                    'status': 'published',
                    'published_at': timezone.now(),
                    'views_count': random.randint(50, 500)
                }
            )
            
            if created:
                # Add tags to post
                for tag_name in post_data['tags']:
                    try:
                        tag = Tag.objects.get(name=tag_name)
                        post.tags.add(tag)
                    except Tag.DoesNotExist:
                        self.stdout.write(f'Tag "{tag_name}" not found, skipping...')
                
                self.stdout.write(f'Created post: {post.title}')

        self.stdout.write(
            self.style.SUCCESS('Successfully populated the blog with sample data!')
        )
