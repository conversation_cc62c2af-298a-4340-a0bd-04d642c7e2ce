from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from blog.models import Category, Tag, Post
from django.utils import timezone
import random


class Command(BaseCommand):
    help = 'Populate the blog with sample data'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample blog data...')

        # Create categories
        categories_data = [
            {'name': 'تطوير الويب', 'description': 'مقالات حول تطوير المواقع والتطبيقات'},
            {'name': 'البرمجة', 'description': 'دروس ونصائح في البرمجة'},
            {'name': 'الذكاء الاصطناعي', 'description': 'آخر التطورات في مجال الذكاء الاصطناعي'},
            {'name': 'أمن المعلومات', 'description': 'مواضيع حول أمن المعلومات والحماية'},
            {'name': 'تقنيات حديثة', 'description': 'أحدث التقنيات والابتكارات'},
        ]

        categories = []
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            categories.append(category)
            if created:
                self.stdout.write(f'Created category: {category.name}')

        # Create tags
        tags_data = [
            'Python', 'Django', 'JavaScript', 'React', 'Vue.js', 'Node.js',
            'HTML', 'CSS', 'Bootstrap', 'Tailwind', 'MySQL', 'PostgreSQL',
            'MongoDB', 'Redis', 'Docker', 'Kubernetes', 'AWS', 'Azure',
            'Git', 'GitHub', 'API', 'REST', 'GraphQL', 'Machine Learning',
            'Deep Learning', 'TensorFlow', 'PyTorch', 'Data Science',
            'Cybersecurity', 'Blockchain', 'IoT', 'Mobile Development'
        ]

        tags = []
        for tag_name in tags_data:
            tag, created = Tag.objects.get_or_create(name=tag_name)
            tags.append(tag)
            if created:
                self.stdout.write(f'Created tag: {tag.name}')

        # Get or create admin user
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'مدير',
                'last_name': 'المدونة',
                'is_staff': True,
                'is_superuser': True
            }
        )

        # Sample posts data
        posts_data = [
            {
                'title': 'مقدمة في تطوير الويب باستخدام Django',
                'content': '''
Django هو إطار عمل قوي لتطوير تطبيقات الويب باستخدام لغة Python. يتميز Django بسهولة الاستخدام والأمان العالي.

## ما هو Django؟

Django هو إطار عمل مفتوح المصدر يتبع نمط Model-View-Template (MVT). تم تطويره لمساعدة المطورين على بناء تطبيقات ويب معقدة بسرعة وكفاءة.

## مميزات Django

- **الأمان**: يوفر Django حماية ضد العديد من الثغرات الأمنية الشائعة
- **قابلية التوسع**: يمكن استخدامه لبناء تطبيقات صغيرة ومشاريع ضخمة
- **المرونة**: يدعم قواعد بيانات متعددة ويمكن تخصيصه بسهولة
- **المجتمع**: يحتوي على مجتمع كبير ونشط من المطورين

## البدء مع Django

لبدء مشروع Django جديد، تحتاج إلى تثبيت Django أولاً:

```bash
pip install django
django-admin startproject myproject
cd myproject
python manage.py runserver
```

هذا سيقوم بإنشاء مشروع Django جديد وتشغيل الخادم المحلي.
                ''',
                'category': 'تطوير الويب',
                'tags': ['Python', 'Django']
            },
            {
                'title': 'أساسيات JavaScript للمبتدئين',
                'content': '''
JavaScript هي لغة البرمجة الأساسية للويب. تستخدم لإضافة التفاعل والديناميكية لصفحات الويب.

## ما هي JavaScript؟

JavaScript هي لغة برمجة عالية المستوى ومفسرة. تعمل في المتصفحات وعلى الخوادم باستخدام Node.js.

## المتغيرات في JavaScript

يمكن إعلان المتغيرات باستخدام `var`، `let`، أو `const`:

```javascript
let name = "أحمد";
const age = 25;
var city = "الرياض";
```

## الدوال

الدوال هي كتل من الكود يمكن إعادة استخدامها:

```javascript
function greet(name) {
    return "مرحباً " + name;
}

console.log(greet("سارة"));
```

## الأحداث

يمكن التعامل مع أحداث المستخدم مثل النقر:

```javascript
document.getElementById("myButton").addEventListener("click", function() {
    alert("تم النقر على الزر!");
});
```

JavaScript لغة قوية ومرنة تستحق التعلم لكل مطور ويب.
                ''',
                'category': 'البرمجة',
                'tags': ['JavaScript']
            },
            {
                'title': 'مقدمة في الذكاء الاصطناعي وتعلم الآلة',
                'content': '''
الذكاء الاصطناعي هو أحد أهم المجالات التقنية في العصر الحديث. يهدف إلى إنشاء أنظمة قادرة على محاكاة الذكاء البشري.

## ما هو الذكاء الاصطناعي؟

الذكاء الاصطناعي (AI) هو فرع من علوم الحاسوب يهدف إلى إنشاء آلات ذكية قادرة على أداء مهام تتطلب ذكاءً بشرياً.

## أنواع الذكاء الاصطناعي

### 1. الذكاء الاصطناعي الضيق (Narrow AI)
- متخصص في مهمة واحدة
- مثل: محركات البحث، أنظمة التوصية

### 2. الذكاء الاصطناعي العام (General AI)
- قادر على أداء أي مهمة ذهنية يمكن للإنسان القيام بها
- لا يزال قيد التطوير

## تعلم الآلة

تعلم الآلة هو فرع من الذكاء الاصطناعي يركز على بناء أنظمة تتعلم من البيانات:

- **التعلم المراقب**: التعلم من بيانات مُصنفة
- **التعلم غير المراقب**: اكتشاف الأنماط في البيانات
- **التعلم المعزز**: التعلم من خلال التجربة والخطأ

## التطبيقات العملية

- التعرف على الصور والكلام
- المساعدات الصوتية
- السيارات ذاتية القيادة
- الترجمة الآلية
- التشخيص الطبي

المستقبل مليء بالإمكانيات اللامحدودة للذكاء الاصطناعي.
                ''',
                'category': 'الذكاء الاصطناعي',
                'tags': ['Python', 'TensorFlow', 'Machine Learning']
            },
            {
                'title': 'أمن المعلومات: أفضل الممارسات للمطورين',
                'content': '''
أمن المعلومات أمر بالغ الأهمية في تطوير التطبيقات. يجب على كل مطور فهم أساسيات الأمان.

## أهمية أمن المعلومات

في عالم متصل رقمياً، حماية البيانات والأنظمة أمر حيوي لنجاح أي مشروع تقني.

## التهديدات الشائعة

### 1. حقن SQL (SQL Injection)
- استخدام استعلامات محضرة (Prepared Statements)
- التحقق من صحة المدخلات

### 2. البرمجة النصية عبر المواقع (XSS)
- تنظيف المدخلات
- استخدام Content Security Policy

### 3. تزوير الطلبات عبر المواقع (CSRF)
- استخدام رموز CSRF
- التحقق من المرجع

## أفضل الممارسات

### التشفير
```python
import hashlib
import bcrypt

# تشفير كلمات المرور
password = "my_password"
hashed = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
```

### التحقق من الهوية
- استخدام JWT للمصادقة
- تطبيق المصادقة متعددة العوامل
- إدارة الجلسات بأمان

### حماية البيانات
- تشفير البيانات الحساسة
- استخدام HTTPS
- النسخ الاحتياطي المنتظم

### مراقبة الأنظمة
- تسجيل الأحداث الأمنية
- مراقبة الأنشطة المشبوهة
- التحديث المستمر للأنظمة

الأمان ليس خياراً، بل ضرورة في كل مشروع تقني.
                ''',
                'category': 'أمن المعلومات',
                'tags': ['Cybersecurity', 'Python']
            },
            {
                'title': 'مقدمة في React: بناء واجهات المستخدم التفاعلية',
                'content': '''
React هي مكتبة JavaScript قوية لبناء واجهات المستخدم. طورتها Facebook وأصبحت من أشهر أدوات تطوير الويب.

## ما هي React؟

React هي مكتبة JavaScript مفتوحة المصدر لبناء واجهات المستخدم، خاصة للتطبيقات أحادية الصفحة.

## المفاهيم الأساسية

### المكونات (Components)
```jsx
function Welcome(props) {
    return <h1>مرحباً، {props.name}</h1>;
}

function App() {
    return (
        <div>
            <Welcome name="أحمد" />
            <Welcome name="فاطمة" />
        </div>
    );
}
```

### الحالة (State)
```jsx
import { useState } from 'react';

function Counter() {
    const [count, setCount] = useState(0);

    return (
        <div>
            <p>العدد: {count}</p>
            <button onClick={() => setCount(count + 1)}>
                زيادة
            </button>
        </div>
    );
}
```

### الخصائص (Props)
الخصائص هي طريقة لتمرير البيانات من مكون أب إلى مكون فرعي.

## مميزات React

- **الأداء العالي**: Virtual DOM يحسن الأداء
- **قابلية إعادة الاستخدام**: المكونات قابلة للاستخدام مرة أخرى
- **النظام البيئي**: مكتبات وأدوات كثيرة
- **المجتمع**: دعم قوي من المطورين

## البدء مع React

```bash
npx create-react-app my-app
cd my-app
npm start
```

React تجعل بناء واجهات المستخدم أسهل وأكثر متعة!
                ''',
                'category': 'تطوير الويب',
                'tags': ['React', 'JavaScript']
            }
        ]

        # Create posts
        for post_data in posts_data:
            category = Category.objects.get(name=post_data['category'])
            
            post, created = Post.objects.get_or_create(
                title=post_data['title'],
                defaults={
                    'content': post_data['content'],
                    'author': admin_user,
                    'category': category,
                    'status': 'published',
                    'published_at': timezone.now(),
                    'views_count': random.randint(50, 500)
                }
            )
            
            if created:
                # Add tags to post
                for tag_name in post_data['tags']:
                    try:
                        tag = Tag.objects.get(name=tag_name)
                        post.tags.add(tag)
                    except Tag.DoesNotExist:
                        self.stdout.write(f'Tag "{tag_name}" not found, skipping...')
                
                self.stdout.write(f'Created post: {post.title}')

        self.stdout.write(
            self.style.SUCCESS('Successfully populated the blog with sample data!')
        )
