from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from django.utils.text import slugify
import uuid


class Category(models.Model):
    name = models.CharField(max_length=100, unique=True, verbose_name="اسم التصنيف")
    slug = models.SlugField(max_length=100, unique=True, verbose_name="الرابط")
    description = models.TextField(blank=True, verbose_name="الوصف")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "تصنيف"
        verbose_name_plural = "التصنيفات"
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            base_slug = slugify(self.name) or str(uuid.uuid4())[:8]
            self.slug = base_slug
            # Ensure unique slug
            counter = 1
            while Category.objects.filter(slug=self.slug).exclude(pk=self.pk).exists():
                self.slug = f"{base_slug}-{counter}"
                counter += 1
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('blog:category_posts', kwargs={'slug': self.slug})


class Tag(models.Model):
    name = models.CharField(max_length=50, unique=True, verbose_name="اسم الوسم")
    slug = models.SlugField(max_length=50, unique=True, verbose_name="الرابط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "وسم"
        verbose_name_plural = "الوسوم"
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            base_slug = slugify(self.name) or str(uuid.uuid4())[:8]
            self.slug = base_slug
            # Ensure unique slug
            counter = 1
            while Tag.objects.filter(slug=self.slug).exclude(pk=self.pk).exists():
                self.slug = f"{base_slug}-{counter}"
                counter += 1
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('blog:tag_posts', kwargs={'slug': self.slug})


class Post(models.Model):
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('published', 'منشور'),
        ('scheduled', 'مجدول'),
    ]

    title = models.CharField(max_length=200, verbose_name="العنوان")
    slug = models.SlugField(max_length=200, unique=True, verbose_name="الرابط")
    author = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="الكاتب")
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name="التصنيف")
    tags = models.ManyToManyField(Tag, blank=True, verbose_name="الوسوم")
    content = models.TextField(verbose_name="المحتوى")
    excerpt = models.TextField(max_length=300, blank=True, verbose_name="المقتطف")
    # featured_image = models.ImageField(upload_to='blog/images/', blank=True, null=True, verbose_name="الصورة المميزة")
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    published_at = models.DateTimeField(blank=True, null=True, verbose_name="تاريخ النشر")
    views_count = models.PositiveIntegerField(default=0, verbose_name="عدد المشاهدات")

    class Meta:
        verbose_name = "مقال"
        verbose_name_plural = "المقالات"
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.slug:
            base_slug = slugify(self.title) or str(uuid.uuid4())[:8]
            self.slug = base_slug
            # Ensure unique slug
            counter = 1
            while Post.objects.filter(slug=self.slug).exclude(pk=self.pk).exists():
                self.slug = f"{base_slug}-{counter}"
                counter += 1
        if self.status == 'published' and not self.published_at:
            self.published_at = timezone.now()
        if not self.excerpt:
            self.excerpt = self.content[:300] + '...' if len(self.content) > 300 else self.content
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('blog:post_detail', kwargs={'slug': self.slug})

    @property
    def is_published(self):
        return self.status == 'published' and self.published_at <= timezone.now()

    def increment_views(self):
        self.views_count += 1
        self.save(update_fields=['views_count'])
