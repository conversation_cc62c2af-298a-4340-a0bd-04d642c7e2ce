{% extends 'base.html' %}

{% block title %}{{ post.title }} - مدونة عربية{% endblock %}

{% block extra_css %}
<style>
    .article-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
    }

    .article-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.3);
        z-index: 1;
    }

    .article-hero > * {
        position: relative;
        z-index: 2;
    }

    .floating-shapes {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 0;
    }

    .floating-shapes::before,
    .floating-shapes::after {
        content: '';
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        animation: float 6s ease-in-out infinite;
    }

    .floating-shapes::before {
        width: 200px;
        height: 200px;
        top: 20%;
        right: 10%;
        animation-delay: -2s;
    }

    .floating-shapes::after {
        width: 150px;
        height: 150px;
        bottom: 20%;
        left: 15%;
        animation-delay: -4s;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .prose {
        max-width: none;
        color: #374151;
    }

    .prose p {
        margin-bottom: 1.8rem;
        line-height: 1.9;
        font-size: 1.1rem;
    }

    .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
        font-weight: 700;
        margin-top: 2.5rem;
        margin-bottom: 1.2rem;
        color: #1f2937;
    }

    .prose h2 {
        font-size: 1.8rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .prose h3 {
        font-size: 1.4rem;
        color: #4f46e5;
    }

    .prose ul, .prose ol {
        margin-bottom: 1.8rem;
        padding-right: 2rem;
    }

    .prose li {
        margin-bottom: 0.8rem;
        position: relative;
    }

    .prose ul li::before {
        content: '▶';
        color: #667eea;
        position: absolute;
        right: -1.5rem;
        font-size: 0.8rem;
    }

    .prose blockquote {
        border-right: 4px solid #667eea;
        background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
        padding: 1.5rem;
        margin: 2rem 0;
        border-radius: 0.5rem;
        font-style: italic;
        color: #4f46e5;
        position: relative;
    }

    .prose blockquote::before {
        content: '"';
        font-size: 4rem;
        color: #667eea;
        position: absolute;
        top: -10px;
        right: 20px;
        opacity: 0.3;
    }

    .prose code {
        background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
        color: #dc2626;
        padding: 0.3rem 0.6rem;
        border-radius: 0.4rem;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .prose pre {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        color: #f9fafb;
        padding: 1.5rem;
        border-radius: 0.8rem;
        overflow-x: auto;
        margin: 2rem 0;
        border: 1px solid #374151;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    .article-content {
        background: linear-gradient(180deg, #ffffff 0%, #f9fafb 100%);
    }

    .share-button {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .share-button:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .tag-cloud {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    }

    .related-post-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .related-post-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }
</style>
{% endblock %}

{% block content %}
<!-- Article Hero Section -->
<section class="article-hero py-20 relative">
    <div class="floating-shapes"></div>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Breadcrumb -->
        <nav class="flex items-center space-x-2 space-x-reverse text-sm text-white opacity-90 mb-8">
            <a href="{% url 'blog:home' %}" class="hover:text-yellow-300 transition-colors">الرئيسية</a>
            <span>←</span>
            <a href="{{ post.category.get_absolute_url }}" class="hover:text-yellow-300 transition-colors">{{ post.category.name }}</a>
            <span>←</span>
            <span class="text-yellow-300">{{ post.title }}</span>
        </nav>

        <div class="text-center">
            <!-- Category Badge -->
            <div class="inline-flex items-center mb-6">
                <span class="bg-white bg-opacity-20 text-white text-sm px-4 py-2 rounded-full font-medium backdrop-blur-sm border border-white border-opacity-30">
                    {{ post.category.name }}
                </span>
                <span class="text-white opacity-75 text-sm mr-4 flex items-center">
                    <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                    </svg>
                    {{ post.published_at|date:"d F Y" }}
                </span>
                <span class="text-white opacity-75 text-sm mr-4 flex items-center">
                    <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                    </svg>
                    {{ post.views_count }} مشاهدة
                </span>
                <span class="text-white opacity-75 text-sm mr-4 flex items-center">
                    <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                    <span id="reading-time">5</span> دقائق قراءة
                </span>
            </div>

            <!-- Title -->
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-8 leading-tight max-w-4xl mx-auto">
                {{ post.title }}
            </h1>

            <!-- Author Info -->
            <div class="flex items-center justify-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white border-opacity-30">
                    <span class="text-white font-bold text-xl">
                        {{ post.author.get_full_name.0|default:post.author.username.0|upper }}
                    </span>
                </div>
                <div class="mr-4 text-right">
                    <p class="text-white font-semibold text-lg">{{ post.author.get_full_name|default:post.author.username }}</p>
                    <p class="text-white opacity-75 text-sm">كاتب ومطور</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Article Content -->
<article class="article-content">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Table of Contents -->
            <div class="lg:col-span-1">
                <div class="sticky top-8">
                    <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
                        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 ml-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                            </svg>
                            فهرس المحتويات
                        </h3>
                        <nav id="table-of-contents" class="space-y-2">
                            <!-- TOC will be generated by JavaScript -->
                        </nav>

                        <!-- Reading Progress -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-gray-600">تقدم القراءة</span>
                                <span id="progress-percentage" class="text-sm font-medium text-primary-600">0%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div id="reading-progress-bar" class="bg-gradient-to-r from-primary-500 to-purple-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>

                        <!-- Article Stats -->
                        <div class="mt-6 pt-6 border-t border-gray-200 space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">الكلمات</span>
                                <span id="word-count" class="text-sm font-medium text-gray-900">0</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">الفقرات</span>
                                <span id="paragraph-count" class="text-sm font-medium text-gray-900">0</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">وقت القراءة</span>
                                <span class="text-sm font-medium text-gray-900"><span id="reading-time-sidebar">5</span> دقائق</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-3">
                <div class="bg-white rounded-3xl shadow-2xl overflow-hidden">
                    <div class="p-8 lg:p-12">
                        <!-- Article Body -->
                        <div id="article-content" class="prose prose-lg max-w-none">
                            {{ post.content|linebreaks }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</article>

<!-- Tags and Share Section -->
<section class="py-12 bg-gradient-to-br from-gray-50 to-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Tags -->
            {% if post.tags.all %}
            <div class="bg-white rounded-2xl shadow-lg p-8 tag-cloud">
                <div class="flex items-center mb-6">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mr-4">الوسوم</h3>
                </div>

                <div class="flex flex-wrap gap-3">
                    {% for tag in post.tags.all %}
                    <a href="{{ tag.get_absolute_url }}" class="inline-block bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 text-sm px-4 py-2 rounded-full hover:from-blue-200 hover:to-indigo-200 transition-all duration-300 transform hover:scale-105 font-medium">
                        #{{ tag.name }}
                    </a>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Share Buttons -->
            <div class="bg-white rounded-2xl shadow-lg p-8">
                <div class="flex items-center mb-6">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mr-4">شارك المقال</h3>
                </div>

                <div class="grid grid-cols-2 gap-3">
                    <a href="https://twitter.com/intent/tweet?text={{ post.title|urlencode }}&url={{ request.build_absolute_uri }}"
                       target="_blank"
                       class="share-button bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-3 rounded-xl text-sm font-medium flex items-center justify-center">
                        <svg class="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                        </svg>
                        تويتر
                    </a>

                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.build_absolute_uri }}"
                       target="_blank"
                       class="share-button bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-xl text-sm font-medium flex items-center justify-center">
                        <svg class="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 24 24">
                            <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
                        </svg>
                        فيسبوك
                    </a>

                    <a href="https://wa.me/?text={{ post.title|urlencode }}%20{{ request.build_absolute_uri }}"
                       target="_blank"
                       class="share-button bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-3 rounded-xl text-sm font-medium flex items-center justify-center">
                        <svg class="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488" />
                        </svg>
                        واتساب
                    </a>

                    <button onclick="copyToClipboard()"
                            class="share-button bg-gradient-to-r from-gray-500 to-gray-600 text-white px-4 py-3 rounded-xl text-sm font-medium flex items-center justify-center">
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        نسخ الرابط
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Posts -->
{% if related_posts %}
<section class="py-16 bg-gradient-to-br from-indigo-50 to-purple-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-4xl font-bold mb-4">
                <span class="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                    مقالات ذات صلة
                </span>
            </h2>
            <p class="text-gray-600 text-lg">
                اكتشف المزيد من المحتوى المشابه الذي قد يهمك
            </p>
            <div class="w-24 h-1 bg-gradient-to-r from-indigo-500 to-purple-500 mx-auto rounded-full mt-4"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for related_post in related_posts %}
            <article class="related-post-card rounded-2xl shadow-xl overflow-hidden border border-gray-100">
                <!-- Card Header -->
                <div class="h-2 bg-gradient-to-r from-indigo-500 to-purple-500"></div>

                <div class="p-6">
                    <!-- Category and Date -->
                    <div class="flex items-center justify-between mb-4">
                        <span class="bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-800 text-xs px-3 py-1 rounded-full font-medium">
                            {{ related_post.category.name }}
                        </span>
                        <span class="text-gray-400 text-xs flex items-center">
                            <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                            </svg>
                            {{ related_post.published_at|date:"d M" }}
                        </span>
                    </div>

                    <!-- Title -->
                    <h3 class="text-xl font-bold text-gray-900 mb-3 leading-tight hover:text-indigo-600 transition-colors duration-300">
                        <a href="{{ related_post.get_absolute_url }}">
                            {{ related_post.title }}
                        </a>
                    </h3>

                    <!-- Excerpt -->
                    <p class="text-gray-600 text-sm mb-6 leading-relaxed">
                        {{ related_post.excerpt|truncatewords:12 }}
                    </p>

                    <!-- Footer -->
                    <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                                {{ related_post.author.get_full_name.0|default:related_post.author.username.0|upper }}
                            </div>
                            <div class="mr-2">
                                <p class="text-gray-900 text-sm font-medium">{{ related_post.author.get_full_name|default:related_post.author.username }}</p>
                                <p class="text-gray-500 text-xs flex items-center">
                                    <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ related_post.views_count }}
                                </p>
                            </div>
                        </div>

                        <a href="{{ related_post.get_absolute_url }}" class="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 transform hover:scale-105">
                            قراءة
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </article>
            {% endfor %}
        </div>

        <!-- View More Button -->
        <div class="text-center mt-12">
            <a href="{% url 'blog:home' %}" class="inline-flex items-center bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                استكشف المزيد من المقالات
            </a>
        </div>
    </div>
</section>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function copyToClipboard() {
    navigator.clipboard.writeText(window.location.href).then(function() {
        // Create a beautiful notification
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-gradient-to-r from-green-500 to-teal-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
        notification.innerHTML = `
            <div class="flex items-center">
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                تم نسخ الرابط بنجاح!
            </div>
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Animate out and remove
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);

    }, function(err) {
        console.error('خطأ في نسخ الرابط: ', err);
        alert('حدث خطأ أثناء نسخ الرابط');
    });
}

// Table of Contents and Reading Progress
function generateTableOfContents() {
    const toc = document.getElementById('table-of-contents');
    const content = document.getElementById('article-content');
    const headings = content.querySelectorAll('h1, h2, h3, h4, h5, h6');

    if (headings.length === 0) {
        toc.innerHTML = '<p class="text-sm text-gray-500">لا توجد عناوين في هذا المقال</p>';
        return;
    }

    let tocHTML = '';
    headings.forEach((heading, index) => {
        const id = `heading-${index}`;
        heading.id = id;

        const level = parseInt(heading.tagName.charAt(1));
        const indent = (level - 1) * 12;

        tocHTML += `
            <a href="#${id}" class="block py-2 px-3 text-sm text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-all duration-200 toc-link" style="margin-right: ${indent}px">
                ${heading.textContent}
            </a>
        `;
    });

    toc.innerHTML = tocHTML;

    // Smooth scroll for TOC links
    document.querySelectorAll('.toc-link').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            const target = document.getElementById(targetId);
            if (target) {
                target.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        });
    });
}

function calculateReadingTime() {
    const content = document.getElementById('article-content');
    const text = content.textContent || content.innerText;
    const words = text.trim().split(/\s+/).length;
    const readingTime = Math.ceil(words / 200); // 200 words per minute

    document.getElementById('reading-time').textContent = readingTime;
    document.getElementById('reading-time-sidebar').textContent = readingTime;

    return { words, readingTime };
}

function updateArticleStats() {
    const content = document.getElementById('article-content');
    const text = content.textContent || content.innerText;
    const words = text.trim().split(/\s+/).length;
    const paragraphs = content.querySelectorAll('p').length;

    document.getElementById('word-count').textContent = words.toLocaleString('ar');
    document.getElementById('paragraph-count').textContent = paragraphs;
}

function updateReadingProgress() {
    const content = document.getElementById('article-content');
    const contentTop = content.offsetTop;
    const contentHeight = content.offsetHeight;
    const windowHeight = window.innerHeight;
    const scrollTop = window.pageYOffset;

    const start = contentTop - windowHeight / 2;
    const end = contentTop + contentHeight - windowHeight / 2;

    if (scrollTop < start) {
        progress = 0;
    } else if (scrollTop > end) {
        progress = 100;
    } else {
        progress = ((scrollTop - start) / (end - start)) * 100;
    }

    const progressBar = document.getElementById('reading-progress-bar');
    const progressText = document.getElementById('progress-percentage');

    if (progressBar && progressText) {
        progressBar.style.width = progress + '%';
        progressText.textContent = Math.round(progress) + '%';
    }

    // Highlight current section in TOC
    const headings = document.querySelectorAll('#article-content h1, #article-content h2, #article-content h3, #article-content h4, #article-content h5, #article-content h6');
    let currentHeading = null;

    headings.forEach(heading => {
        const rect = heading.getBoundingClientRect();
        if (rect.top <= 100 && rect.bottom >= 0) {
            currentHeading = heading;
        }
    });

    // Remove active class from all TOC links
    document.querySelectorAll('.toc-link').forEach(link => {
        link.classList.remove('bg-primary-100', 'text-primary-700', 'font-medium');
        link.classList.add('text-gray-600');
    });

    // Add active class to current heading's TOC link
    if (currentHeading) {
        const activeLink = document.querySelector(`a[href="#${currentHeading.id}"]`);
        if (activeLink) {
            activeLink.classList.remove('text-gray-600');
            activeLink.classList.add('bg-primary-100', 'text-primary-700', 'font-medium');
        }
    }
}

// Enhanced scroll animations for article content
document.addEventListener('DOMContentLoaded', function() {
    // Initialize article features
    generateTableOfContents();
    calculateReadingTime();
    updateArticleStats();

    // Update reading progress on scroll
    window.addEventListener('scroll', updateReadingProgress);
    updateReadingProgress(); // Initial call
    // Animate article sections on scroll
    gsap.utils.toArray('.prose p, .prose h2, .prose h3, .prose ul, .prose ol, .prose blockquote').forEach((element, index) => {
        gsap.fromTo(element,
            { opacity: 0, y: 30 },
            {
                opacity: 1,
                y: 0,
                duration: 0.8,
                delay: index * 0.1,
                ease: "power3.out",
                scrollTrigger: {
                    trigger: element,
                    start: "top 85%",
                    toggleActions: "play none none reverse"
                }
            }
        );
    });

    // Animate share buttons
    gsap.utils.toArray('.share-button').forEach((button, index) => {
        gsap.fromTo(button,
            { opacity: 0, scale: 0.8 },
            {
                opacity: 1,
                scale: 1,
                duration: 0.5,
                delay: index * 0.1,
                ease: "back.out(1.7)",
                scrollTrigger: {
                    trigger: button,
                    start: "top 90%",
                    toggleActions: "play none none reverse"
                }
            }
        );
    });

    // Animate tags
    gsap.utils.toArray('.tag-cloud a').forEach((tag, index) => {
        gsap.fromTo(tag,
            { opacity: 0, y: 20 },
            {
                opacity: 1,
                y: 0,
                duration: 0.4,
                delay: index * 0.05,
                ease: "power2.out",
                scrollTrigger: {
                    trigger: tag,
                    start: "top 90%",
                    toggleActions: "play none none reverse"
                }
            }
        );
    });

    // Animate related posts
    gsap.utils.toArray('.related-post-card').forEach((card, index) => {
        gsap.fromTo(card,
            { opacity: 0, y: 50, scale: 0.9 },
            {
                opacity: 1,
                y: 0,
                scale: 1,
                duration: 0.6,
                delay: index * 0.15,
                ease: "power3.out",
                scrollTrigger: {
                    trigger: card,
                    start: "top 85%",
                    toggleActions: "play none none reverse"
                }
            }
        );
    });
});
</script>
{% endblock %}
