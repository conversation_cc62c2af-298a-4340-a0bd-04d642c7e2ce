{% extends 'base.html' %}

{% block title %}{{ tag.name }} - مدونة عربية{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500 mb-6">
        <a href="{% url 'blog:home' %}" class="hover:text-primary-600 transition-colors">الرئيسية</a>
        <span>←</span>
        <span class="text-gray-900">الوسم: {{ tag.name }}</span>
    </nav>

    <!-- Tag Header -->
    <div class="bg-white rounded-lg shadow-md p-8 mb-8">
        <div class="flex items-center mb-4">
            <span class="bg-primary-100 text-primary-800 text-lg px-4 py-2 rounded-full font-medium">
                {{ tag.name }}
            </span>
        </div>
        <h1 class="text-3xl font-bold text-gray-900 mb-4">مقالات بوسم "{{ tag.name }}"</h1>
        <div class="text-sm text-gray-500">
            {{ page_obj.paginator.count }} مقال بهذا الوسم
        </div>
    </div>

    <!-- Posts Grid -->
    {% if page_obj.object_list %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {% for post in page_obj.object_list %}
        <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <div class="p-6">
                <div class="flex items-center mb-3">
                    <span class="bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full">
                        {{ post.category.name }}
                    </span>
                    <span class="text-gray-500 text-sm mr-3">
                        {{ post.published_at|date:"d F Y" }}
                    </span>
                </div>
                <h2 class="text-lg font-semibold text-gray-900 mb-3">
                    <a href="{{ post.get_absolute_url }}" class="hover:text-primary-600 transition-colors">
                        {{ post.title }}
                    </a>
                </h2>
                <p class="text-gray-600 text-sm mb-4 leading-relaxed">
                    {{ post.excerpt|truncatewords:20 }}
                </p>
                <div class="flex items-center justify-between">
                    <div class="flex items-center text-xs text-gray-500">
                        <span>{{ post.author.get_full_name|default:post.author.username }}</span>
                        <span class="mx-2">•</span>
                        <span>{{ post.views_count }} مشاهدة</span>
                    </div>
                    <a href="{{ post.get_absolute_url }}" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                        اقرأ المزيد ←
                    </a>
                </div>
                
                <!-- Other Tags -->
                {% if post.tags.all %}
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex flex-wrap gap-1">
                        {% for post_tag in post.tags.all|slice:":3" %}
                        <a href="{{ post_tag.get_absolute_url }}" class="inline-block {% if post_tag == tag %}bg-primary-100 text-primary-800{% else %}bg-gray-100 text-gray-600 hover:bg-primary-100 hover:text-primary-800{% endif %} text-xs px-2 py-1 rounded-full transition-colors">
                            {{ post_tag.name }}
                        </a>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </article>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="flex justify-center">
        <nav class="flex items-center space-x-1 space-x-reverse">
            {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-primary-600 transition-colors">
                    السابق
                </a>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <span class="px-3 py-2 text-sm text-white bg-primary-600 border border-primary-600">
                        {{ num }}
                    </span>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <a href="?page={{ num }}" class="px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 hover:text-primary-600 transition-colors">
                        {{ num }}
                    </a>
                {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-primary-600 transition-colors">
                    التالي
                </a>
            {% endif %}
        </nav>
    </div>
    {% endif %}

    {% else %}
    <!-- Empty State -->
    <div class="bg-white rounded-lg shadow-md p-12 text-center">
        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
            </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد مقالات</h3>
        <p class="text-gray-500 mb-6">لا توجد مقالات بوسم "{{ tag.name }}" حالياً.</p>
        <a href="{% url 'blog:home' %}" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-md font-medium transition-colors">
            العودة للرئيسية
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}
