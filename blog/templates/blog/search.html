{% extends 'base.html' %}

{% block title %}البحث{% if query %} - {{ query }}{% endif %} - مدونة عربية{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-500 mb-6">
        <a href="{% url 'blog:home' %}" class="hover:text-primary-600 transition-colors">الرئيسية</a>
        <span>←</span>
        <span class="text-gray-900">البحث</span>
    </nav>

    <!-- Search Header -->
    <div class="bg-white rounded-lg shadow-md p-8 mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-6">البحث في المقالات</h1>
        
        <!-- Search Form -->
        <form method="get" class="mb-6" id="search-form">
            <div class="flex max-w-2xl relative">
                <input type="text" name="q" value="{{ query }}" placeholder="ابحث في المقالات..."
                       class="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-lg focus-ring"
                       id="search-input">
                <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-l-lg font-medium transition-colors hover-lift focus-ring">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>

                <!-- Loading spinner -->
                <div id="search-loading" class="absolute left-3 top-1/2 transform -translate-y-1/2 hidden">
                    <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600"></div>
                </div>
            </div>
        </form>

        <!-- Search Results Info -->
        {% if query %}
        <div class="text-sm text-gray-600">
            {% if page_obj.object_list %}
                تم العثور على {{ page_obj.paginator.count }} نتيجة للبحث عن "<span class="font-semibold text-gray-900">{{ query }}</span>"
            {% else %}
                لم يتم العثور على نتائج للبحث عن "<span class="font-semibold text-gray-900">{{ query }}</span>"
            {% endif %}
        </div>
        {% endif %}
    </div>

    <!-- Search Results -->
    {% if query %}
        {% if page_obj.object_list %}
        <div class="space-y-6 mb-8">
            {% for post in page_obj.object_list %}
            <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <div class="p-6">
                    <div class="flex items-center mb-3">
                        <span class="bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full">
                            {{ post.category.name }}
                        </span>
                        <span class="text-gray-500 text-sm mr-3">
                            {{ post.published_at|date:"d F Y" }}
                        </span>
                        <span class="text-gray-500 text-sm mr-3">
                            {{ post.views_count }} مشاهدة
                        </span>
                    </div>
                    
                    <h2 class="text-xl font-semibold text-gray-900 mb-3">
                        <a href="{{ post.get_absolute_url }}" class="hover:text-primary-600 transition-colors">
                            {{ post.title }}
                        </a>
                    </h2>
                    
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        {{ post.excerpt|truncatewords:30 }}
                    </p>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center text-sm text-gray-500">
                            <span>بواسطة {{ post.author.get_full_name|default:post.author.username }}</span>
                        </div>
                        <a href="{{ post.get_absolute_url }}" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                            اقرأ المزيد
                        </a>
                    </div>
                    
                    <!-- Tags -->
                    {% if post.tags.all %}
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <div class="flex flex-wrap gap-2">
                            {% for tag in post.tags.all|slice:":5" %}
                            <a href="{{ tag.get_absolute_url }}" class="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full hover:bg-primary-100 hover:text-primary-800 transition-colors">
                                {{ tag.name }}
                            </a>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </article>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="flex justify-center">
            <nav class="flex items-center space-x-1 space-x-reverse">
                {% if page_obj.has_previous %}
                    <a href="?q={{ query }}&page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-primary-600 transition-colors">
                        السابق
                    </a>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="px-3 py-2 text-sm text-white bg-primary-600 border border-primary-600">
                            {{ num }}
                        </span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?q={{ query }}&page={{ num }}" class="px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 hover:text-primary-600 transition-colors">
                            {{ num }}
                        </a>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <a href="?q={{ query }}&page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-primary-600 transition-colors">
                        التالي
                    </a>
                {% endif %}
            </nav>
        </div>
        {% endif %}

        {% else %}
        <!-- No Results -->
        <div class="bg-white rounded-lg shadow-md p-12 text-center">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد نتائج</h3>
            <p class="text-gray-500 mb-6">لم يتم العثور على مقالات تحتوي على "{{ query }}"</p>
            <div class="space-y-2 text-sm text-gray-600">
                <p>جرب:</p>
                <ul class="list-disc list-inside space-y-1">
                    <li>التأكد من صحة الكتابة</li>
                    <li>استخدام كلمات مختلفة أو أكثر عمومية</li>
                    <li>استخدام كلمات مفتاحية أقل</li>
                </ul>
            </div>
        </div>
        {% endif %}
    {% else %}
    <!-- Search Instructions -->
    <div class="bg-white rounded-lg shadow-md p-12 text-center">
        <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">ابحث في المقالات</h3>
        <p class="text-gray-500 mb-6">استخدم مربع البحث أعلاه للعثور على المقالات التي تهمك</p>
        <a href="{% url 'blog:home' %}" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-md font-medium transition-colors">
            تصفح جميع المقالات
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('search-form');
    const searchInput = document.getElementById('search-input');
    const searchLoading = document.getElementById('search-loading');

    // Show loading spinner on form submit
    searchForm.addEventListener('submit', function() {
        searchLoading.classList.remove('hidden');
    });

    // Auto-focus search input
    if (searchInput) {
        searchInput.focus();

        // Add search suggestions (basic implementation)
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length > 2) {
                searchTimeout = setTimeout(() => {
                    // Here you could implement live search suggestions
                    console.log('Searching for:', query);
                }, 300);
            }
        });
    }

    // Animate search results
    gsap.utils.toArray('article').forEach((result, index) => {
        gsap.fromTo(result,
            { opacity: 0, y: 30 },
            {
                opacity: 1,
                y: 0,
                duration: 0.5,
                delay: index * 0.1,
                ease: "power2.out"
            }
        );
    });

    // Highlight search terms in results
    const query = '{{ query|escapejs }}';
    if (query) {
        const regex = new RegExp(`(${query})`, 'gi');
        document.querySelectorAll('article h2, article p').forEach(element => {
            element.innerHTML = element.innerHTML.replace(regex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>');
        });
    }
});
</script>
{% endblock %}
