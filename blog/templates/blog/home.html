{% extends 'base.html' %}

{% block title %}الرئيسية - مدونة عربية{% endblock %}

{% block extra_css %}
<style>
    .hero-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .card-hover {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .card-hover:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    .text-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .floating-animation {
        animation: floating 6s ease-in-out infinite;
    }

    @keyframes floating {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }

    .pattern-bg {
        background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0);
        background-size: 20px 20px;
    }

    .glass-effect {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .particle {
        position: absolute;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        pointer-events: none;
        animation: particle-float 8s infinite linear;
    }

    @keyframes particle-float {
        0% {
            transform: translateY(100vh) rotate(0deg);
            opacity: 0;
        }
        10% {
            opacity: 1;
        }
        90% {
            opacity: 1;
        }
        100% {
            transform: translateY(-100px) rotate(360deg);
            opacity: 0;
        }
    }

    .sparkle {
        position: absolute;
        background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, transparent 70%);
        border-radius: 50%;
        pointer-events: none;
        animation: sparkle 2s infinite ease-in-out;
    }

    @keyframes sparkle {
        0%, 100% {
            opacity: 0;
            transform: scale(0);
        }
        50% {
            opacity: 1;
            transform: scale(1);
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-gradient pattern-bg relative overflow-hidden">
    <div class="absolute inset-0 bg-black opacity-20"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div class="text-center">
            <h1 class="text-5xl md:text-6xl font-bold text-white mb-6 floating-animation">
                مدونة عربية
            </h1>
            <p class="text-xl text-white opacity-90 mb-8 max-w-2xl mx-auto leading-relaxed">
                منصتك المتخصصة في نشر المحتوى التقني والمعرفي باللغة العربية
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <a href="#latest-posts" class="glass-effect text-white px-8 py-3 rounded-full font-medium hover:bg-white hover:text-gray-900 transition-all duration-300 transform hover:scale-105">
                    استكشف المقالات
                </a>
                <a href="{% url 'blog:about' %}" class="border-2 border-white text-white px-8 py-3 rounded-full font-medium hover:bg-white hover:text-gray-900 transition-all duration-300 transform hover:scale-105">
                    من نحن
                </a>
            </div>

            <!-- Statistics -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
                <div class="text-center glass-effect rounded-2xl p-6">
                    <div class="text-3xl font-bold text-white mb-2" data-count="{{ page_obj.paginator.count }}">0</div>
                    <div class="text-white opacity-90 text-sm">مقال</div>
                </div>
                <div class="text-center glass-effect rounded-2xl p-6">
                    <div class="text-3xl font-bold text-white mb-2" data-count="{{ categories.count }}">0</div>
                    <div class="text-white opacity-90 text-sm">تصنيف</div>
                </div>
                <div class="text-center glass-effect rounded-2xl p-6">
                    <div class="text-3xl font-bold text-white mb-2" data-count="{{ popular_tags.count }}">0</div>
                    <div class="text-white opacity-90 text-sm">وسم</div>
                </div>
                <div class="text-center glass-effect rounded-2xl p-6">
                    <div class="text-3xl font-bold text-white mb-2" data-count="1000">0</div>
                    <div class="text-white opacity-90 text-sm">قارئ</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating shapes -->
    <div class="absolute top-20 right-20 w-20 h-20 bg-white opacity-10 rounded-full floating-animation" style="animation-delay: -2s;"></div>
    <div class="absolute bottom-20 left-20 w-16 h-16 bg-white opacity-10 rounded-full floating-animation" style="animation-delay: -4s;"></div>
    <div class="absolute top-1/2 left-1/4 w-12 h-12 bg-white opacity-10 rounded-full floating-animation" style="animation-delay: -1s;"></div>

    <!-- Particle Container -->
    <div id="particles-container" class="absolute inset-0 overflow-hidden pointer-events-none"></div>

    <!-- Sparkles -->
    <div class="absolute top-1/4 right-1/3 w-2 h-2 sparkle" style="animation-delay: 0s;"></div>
    <div class="absolute top-3/4 left-1/4 w-3 h-3 sparkle" style="animation-delay: 1s;"></div>
    <div class="absolute top-1/2 right-1/4 w-1 h-1 sparkle" style="animation-delay: 0.5s;"></div>
    <div class="absolute bottom-1/3 left-1/3 w-2 h-2 sparkle" style="animation-delay: 1.5s;"></div>
</section>

<!-- Featured Post Section -->
{% if page_obj.object_list %}
    {% with page_obj.object_list.0 as featured_post %}
    <section class="py-16 bg-gradient-to-br from-gray-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-gradient mb-4">المقال المميز</h2>
                <div class="w-24 h-1 bg-gradient-to-r from-primary-500 to-purple-500 mx-auto rounded-full"></div>
            </div>

            <div class="bg-white rounded-3xl shadow-2xl overflow-hidden card-hover featured-post">
                <div class="md:flex">
                    <div class="md:w-1/2 p-8 lg:p-12">
                        <div class="flex items-center mb-6">
                            <span class="bg-gradient-to-r from-primary-500 to-purple-500 text-white text-sm px-4 py-2 rounded-full font-medium">
                                {{ featured_post.category.name }}
                            </span>
                            <span class="text-gray-500 text-sm mr-4 flex items-center">
                                <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                </svg>
                                {{ featured_post.published_at|date:"d F Y" }}
                            </span>
                        </div>

                        <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6 leading-tight">
                            <a href="{{ featured_post.get_absolute_url }}" class="hover:text-primary-600 transition-colors">
                                {{ featured_post.title }}
                            </a>
                        </h1>

                        <p class="text-gray-600 mb-8 leading-relaxed text-lg">
                            {{ featured_post.excerpt }}
                        </p>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-r from-primary-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                    {{ featured_post.author.get_full_name.0|default:featured_post.author.username.0|upper }}
                                </div>
                                <div class="mr-3">
                                    <p class="text-gray-900 font-medium">{{ featured_post.author.get_full_name|default:featured_post.author.username }}</p>
                                    <p class="text-gray-500 text-sm flex items-center">
                                        <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        {{ featured_post.views_count }} مشاهدة
                                    </p>
                                </div>
                            </div>

                            <a href="{{ featured_post.get_absolute_url }}" class="bg-gradient-to-r from-primary-600 to-purple-600 hover:from-primary-700 hover:to-purple-700 text-white px-8 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                                اقرأ المزيد
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <div class="md:w-1/2 bg-gradient-to-br from-primary-100 to-purple-100 flex items-center justify-center p-8">
                        <div class="text-center">
                            <div class="w-32 h-32 bg-gradient-to-r from-primary-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4 floating-animation">
                                <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-800 mb-2">مقال مميز</h3>
                            <p class="text-gray-600">اكتشف أحدث المحتوى التقني</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    {% endwith %}
{% endif %}

<!-- Trending Topics Section -->
<section class="py-16 bg-white relative overflow-hidden">
    <div class="absolute inset-0 bg-gradient-to-r from-blue-50 to-purple-50 opacity-50"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-4xl font-bold text-gradient mb-4">المواضيع الرائجة</h2>
            <p class="text-gray-600 text-lg">اكتشف أحدث التقنيات والمواضيع المطلوبة في سوق العمل</p>
            <div class="w-24 h-1 bg-gradient-to-r from-primary-500 to-purple-500 mx-auto rounded-full mt-4"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="trending-card bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white transform hover:scale-105 transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mr-3">الذكاء الاصطناعي</h3>
                </div>
                <p class="text-blue-100 text-sm">تعلم أحدث تقنيات الذكاء الاصطناعي والتعلم الآلي</p>
                <div class="mt-4 flex items-center">
                    <span class="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-full">🔥 رائج</span>
                </div>
            </div>

            <div class="trending-card bg-gradient-to-br from-green-500 to-green-600 rounded-2xl p-6 text-white transform hover:scale-105 transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mr-3">تطوير الويب</h3>
                </div>
                <p class="text-green-100 text-sm">أحدث أطر العمل وتقنيات تطوير المواقع</p>
                <div class="mt-4 flex items-center">
                    <span class="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-full">⭐ مطلوب</span>
                </div>
            </div>

            <div class="trending-card bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl p-6 text-white transform hover:scale-105 transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mr-3">علم البيانات</h3>
                </div>
                <p class="text-purple-100 text-sm">تحليل البيانات والإحصاءات المتقدمة</p>
                <div class="mt-4 flex items-center">
                    <span class="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-full">📈 نمو</span>
                </div>
            </div>

            <div class="trending-card bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl p-6 text-white transform hover:scale-105 transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mr-3">أمن المعلومات</h3>
                </div>
                <p class="text-orange-100 text-sm">حماية الأنظمة والشبكات من التهديدات</p>
                <div class="mt-4 flex items-center">
                    <span class="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-full">🛡️ حيوي</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Latest Posts Section -->
<section id="latest-posts" class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-4xl font-bold text-gradient mb-4">أحدث المقالات</h2>
            <p class="text-gray-600 text-lg max-w-2xl mx-auto">
                اكتشف آخر المقالات التقنية والمعرفية المكتوبة خصيصاً للمجتمع العربي
            </p>
            <div class="w-24 h-1 bg-gradient-to-r from-primary-500 to-purple-500 mx-auto rounded-full mt-4"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {% for post in page_obj.object_list|slice:"1:" %}
            <article class="post-card group bg-white rounded-2xl shadow-lg overflow-hidden card-hover border border-gray-100">
                <!-- Card Header with Gradient -->
                <div class="h-2 bg-gradient-to-r from-primary-500 to-purple-500"></div>

                <div class="p-6">
                    <!-- Category and Date -->
                    <div class="flex items-center justify-between mb-4">
                        <span class="bg-gradient-to-r from-primary-100 to-purple-100 text-primary-800 text-xs px-3 py-1 rounded-full font-medium">
                            {{ post.category.name }}
                        </span>
                        <span class="text-gray-400 text-xs flex items-center">
                            <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                            </svg>
                            {{ post.published_at|date:"d M" }}
                        </span>
                    </div>

                    <!-- Title -->
                    <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-300 leading-tight">
                        <a href="{{ post.get_absolute_url }}">
                            {{ post.title }}
                        </a>
                    </h3>

                    <!-- Excerpt -->
                    <p class="text-gray-600 text-sm mb-6 leading-relaxed line-clamp-3">
                        {{ post.excerpt|truncatewords:15 }}
                    </p>

                    <!-- Footer -->
                    <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-purple-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                                {{ post.author.get_full_name.0|default:post.author.username.0|upper }}
                            </div>
                            <div class="mr-2">
                                <p class="text-gray-900 text-sm font-medium">{{ post.author.get_full_name|default:post.author.username }}</p>
                                <p class="text-gray-500 text-xs flex items-center">
                                    <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ post.views_count }}
                                </p>
                            </div>
                        </div>

                        <a href="{{ post.get_absolute_url }}" class="text-primary-600 hover:text-primary-700 font-medium text-sm flex items-center group-hover:translate-x-1 transition-transform duration-300">
                            قراءة
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </article>
            {% empty %}
            <div class="col-span-full text-center py-16">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-medium text-gray-900 mb-2">لا توجد مقالات</h3>
                <p class="text-gray-500">لا توجد مقالات متاحة حالياً.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Pagination -->
{% if page_obj.has_other_pages %}
<section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-center">
            <nav class="flex items-center space-x-2 space-x-reverse">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-primary-50 hover:text-primary-600 hover:border-primary-300 transition-all duration-300">
                        <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
                        </svg>
                        السابق
                    </a>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-primary-600 to-purple-600 border border-primary-600 rounded-lg shadow-lg">
                            {{ num }}
                        </span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-primary-50 hover:text-primary-600 hover:border-primary-300 transition-all duration-300">
                            {{ num }}
                        </a>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-primary-50 hover:text-primary-600 hover:border-primary-300 transition-all duration-300">
                        التالي
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
                        </svg>
                    </a>
                {% endif %}
            </nav>
        </div>
    </div>
</section>
{% endif %}

<!-- Sidebar Section -->
<section class="py-16 bg-gradient-to-br from-gray-50 to-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Search Widget -->
            <div class="bg-white rounded-2xl shadow-lg p-8 card-hover">
                <div class="flex items-center mb-6">
                    <div class="w-12 h-12 bg-gradient-to-r from-primary-500 to-purple-500 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mr-4">البحث السريع</h3>
                </div>

                <form method="get" action="{% url 'blog:search' %}" class="space-y-4">
                    <div class="relative">
                        <input type="text" name="q" placeholder="ابحث في المقالات..."
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300 pr-12">
                        <button type="submit" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary-600 transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Categories Widget -->
            <div class="bg-white rounded-2xl shadow-lg p-8 card-hover">
                <div class="flex items-center mb-6">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mr-4">التصنيفات</h3>
                </div>

                <div class="space-y-3">
                    {% for category in categories %}
                    <a href="{{ category.get_absolute_url }}" class="flex items-center justify-between p-3 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-teal-50 transition-all duration-300 group">
                        <span class="text-gray-700 group-hover:text-green-600 font-medium">{{ category.name }}</span>
                        <svg class="w-4 h-4 text-gray-400 group-hover:text-green-600 transform group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    {% endfor %}
                </div>
            </div>

            <!-- Tags Widget -->
            <div class="bg-white rounded-2xl shadow-lg p-8 card-hover">
                <div class="flex items-center mb-6">
                    <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mr-4">الوسوم الشائعة</h3>
                </div>

                <div class="flex flex-wrap gap-2">
                    {% for tag in popular_tags %}
                    <a href="{{ tag.get_absolute_url }}" class="inline-block bg-gradient-to-r from-orange-100 to-red-100 text-orange-800 text-sm px-4 py-2 rounded-full hover:from-orange-200 hover:to-red-200 transition-all duration-300 transform hover:scale-105">
                        {{ tag.name }}
                    </a>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="py-20 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 relative overflow-hidden">
    <div class="absolute inset-0 bg-black opacity-20"></div>

    <!-- Animated background elements -->
    <div class="absolute top-10 left-10 w-32 h-32 bg-white opacity-5 rounded-full floating-animation"></div>
    <div class="absolute bottom-10 right-10 w-24 h-24 bg-white opacity-5 rounded-full floating-animation" style="animation-delay: -3s;"></div>
    <div class="absolute top-1/2 left-1/3 w-16 h-16 bg-white opacity-5 rounded-full floating-animation" style="animation-delay: -1.5s;"></div>

    <div class="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="mb-8">
            <h2 class="text-4xl md:text-5xl font-bold text-white mb-4">
                ابق على اطلاع دائم
            </h2>
            <p class="text-xl text-white opacity-90 max-w-2xl mx-auto">
                اشترك في نشرتنا البريدية لتصلك أحدث المقالات التقنية والمعرفية مباشرة إلى بريدك الإلكتروني
            </p>
        </div>

        <form class="max-w-md mx-auto" onsubmit="handleNewsletterSubmit(event)">
            <div class="flex flex-col sm:flex-row gap-4">
                <input
                    type="email"
                    placeholder="أدخل بريدك الإلكتروني"
                    required
                    class="flex-1 px-6 py-4 rounded-full text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-white focus:ring-opacity-30 transition-all duration-300"
                >
                <button
                    type="submit"
                    class="bg-white text-purple-900 px-8 py-4 rounded-full font-bold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                    اشترك الآن
                </button>
            </div>
            <p class="text-white opacity-75 text-sm mt-4">
                لن نرسل لك رسائل مزعجة. يمكنك إلغاء الاشتراك في أي وقت.
            </p>
        </form>

        <!-- Social proof -->
        <div class="mt-12 flex items-center justify-center space-x-8 space-x-reverse">
            <div class="text-center">
                <div class="text-2xl font-bold text-white">500+</div>
                <div class="text-white opacity-75 text-sm">مشترك</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-white">50+</div>
                <div class="text-white opacity-75 text-sm">مقال</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-white">10+</div>
                <div class="text-white opacity-75 text-sm">تصنيف</div>
            </div>
        </div>
    </div>
</section>

<script>
function handleNewsletterSubmit(event) {
    event.preventDefault();
    const email = event.target.querySelector('input[type="email"]').value;

    // Create success notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-gradient-to-r from-green-500 to-teal-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
    notification.innerHTML = `
        <div class="flex items-center">
            <svg class="w-6 h-6 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <div>
                <div class="font-bold">تم الاشتراك بنجاح!</div>
                <div class="text-sm opacity-90">شكراً لك على الاشتراك في نشرتنا البريدية</div>
            </div>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 4000);

    // Reset form
    event.target.reset();
}
</script>

<script>
// Particle system for hero section
function createParticle() {
    const container = document.getElementById('particles-container');
    if (!container) return;

    const particle = document.createElement('div');
    particle.className = 'particle';

    // Random size between 2px and 6px
    const size = Math.random() * 4 + 2;
    particle.style.width = size + 'px';
    particle.style.height = size + 'px';

    // Random horizontal position
    particle.style.left = Math.random() * 100 + '%';

    // Random animation duration
    particle.style.animationDuration = (Math.random() * 3 + 5) + 's';

    // Random delay
    particle.style.animationDelay = Math.random() * 2 + 's';

    container.appendChild(particle);

    // Remove particle after animation
    setTimeout(() => {
        if (container.contains(particle)) {
            container.removeChild(particle);
        }
    }, 8000);
}

// Statistics counter animation
function animateCounters() {
    const counters = document.querySelectorAll('[data-count]');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-count'));
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current).toLocaleString('ar');
        }, 16);
    });
}

// Intersection Observer for statistics animation
function observeStatistics() {
    const statsSection = document.querySelector('.hero-gradient');
    if (!statsSection) return;

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounters();
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    observer.observe(statsSection);
}

// Create particles periodically
document.addEventListener('DOMContentLoaded', function() {
    // Initialize statistics animation
    observeStatistics();
    // Create initial particles
    for (let i = 0; i < 5; i++) {
        setTimeout(createParticle, i * 1000);
    }

    // Continue creating particles
    setInterval(createParticle, 2000);

    // Enhanced hover effects for post cards
    gsap.utils.toArray('.post-card').forEach(card => {
        card.addEventListener('mouseenter', () => {
            gsap.to(card, {
                rotationY: 5,
                rotationX: 5,
                duration: 0.3,
                ease: "power2.out",
                transformPerspective: 1000
            });
        });

        card.addEventListener('mouseleave', () => {
            gsap.to(card, {
                rotationY: 0,
                rotationX: 0,
                duration: 0.3,
                ease: "power2.out"
            });
        });
    });

    // Magnetic effect for buttons
    gsap.utils.toArray('.btn-primary, .share-button').forEach(button => {
        button.addEventListener('mousemove', (e) => {
            const rect = button.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;

            gsap.to(button, {
                x: x * 0.1,
                y: y * 0.1,
                duration: 0.3,
                ease: "power2.out"
            });
        });

        button.addEventListener('mouseleave', () => {
            gsap.to(button, {
                x: 0,
                y: 0,
                duration: 0.3,
                ease: "power2.out"
            });
        });
    });
});
</script>
{% endblock %}
