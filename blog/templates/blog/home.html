{% extends 'base.html' %}

{% block title %}الرئيسية - مدونة عربية{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Hero Section -->
            {% if page_obj.object_list %}
                {% with page_obj.object_list.0 as featured_post %}
                <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <span class="bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full">
                                {{ featured_post.category.name }}
                            </span>
                            <span class="text-gray-500 text-sm mr-3">
                                {{ featured_post.published_at|date:"d F Y" }}
                            </span>
                        </div>
                        <h1 class="text-2xl font-bold text-gray-900 mb-4">
                            <a href="{{ featured_post.get_absolute_url }}" class="hover:text-primary-600 transition-colors">
                                {{ featured_post.title }}
                            </a>
                        </h1>
                        <p class="text-gray-600 mb-4 leading-relaxed">
                            {{ featured_post.excerpt }}
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="text-sm text-gray-500">بواسطة {{ featured_post.author.get_full_name|default:featured_post.author.username }}</span>
                                <span class="text-gray-300 mx-2">•</span>
                                <span class="text-sm text-gray-500">{{ featured_post.views_count }} مشاهدة</span>
                            </div>
                            <a href="{{ featured_post.get_absolute_url }}" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                                اقرأ المزيد
                            </a>
                        </div>
                    </div>
                </div>
                {% endwith %}
            {% endif %}

            <!-- Posts Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                {% for post in page_obj.object_list|slice:"1:" %}
                <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full">
                                {{ post.category.name }}
                            </span>
                            <span class="text-gray-500 text-sm mr-3">
                                {{ post.published_at|date:"d F Y" }}
                            </span>
                        </div>
                        <h2 class="text-lg font-semibold text-gray-900 mb-3">
                            <a href="{{ post.get_absolute_url }}" class="hover:text-primary-600 transition-colors">
                                {{ post.title }}
                            </a>
                        </h2>
                        <p class="text-gray-600 text-sm mb-4 leading-relaxed">
                            {{ post.excerpt|truncatewords:20 }}
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-xs text-gray-500">
                                <span>{{ post.author.get_full_name|default:post.author.username }}</span>
                                <span class="mx-2">•</span>
                                <span>{{ post.views_count }} مشاهدة</span>
                            </div>
                            <a href="{{ post.get_absolute_url }}" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                                اقرأ المزيد ←
                            </a>
                        </div>
                    </div>
                </article>
                {% empty %}
                <div class="col-span-2 text-center py-12">
                    <p class="text-gray-500 text-lg">لا توجد مقالات متاحة حالياً.</p>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="flex justify-center">
                <nav class="flex items-center space-x-1 space-x-reverse">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-primary-600 transition-colors">
                            السابق
                        </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="px-3 py-2 text-sm text-white bg-primary-600 border border-primary-600">
                                {{ num }}
                            </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?page={{ num }}" class="px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 hover:text-primary-600 transition-colors">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-primary-600 transition-colors">
                            التالي
                        </a>
                    {% endif %}
                </nav>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Search -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">البحث</h3>
                <form method="get" action="{% url 'blog:search' %}">
                    <div class="flex">
                        <input type="text" name="q" placeholder="ابحث في المقالات..." 
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-l-md transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Categories -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">التصنيفات</h3>
                <ul class="space-y-2">
                    {% for category in categories %}
                    <li>
                        <a href="{{ category.get_absolute_url }}" class="text-gray-600 hover:text-primary-600 transition-colors">
                            {{ category.name }}
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </div>

            <!-- Popular Tags -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">الوسوم الشائعة</h3>
                <div class="flex flex-wrap gap-2">
                    {% for tag in popular_tags %}
                    <a href="{{ tag.get_absolute_url }}" class="inline-block bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full hover:bg-primary-200 transition-colors">
                        {{ tag.name }}
                    </a>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
